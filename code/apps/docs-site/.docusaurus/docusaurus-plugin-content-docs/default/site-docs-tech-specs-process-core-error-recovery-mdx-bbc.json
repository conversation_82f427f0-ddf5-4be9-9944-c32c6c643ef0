{"id": "process/core/error-recovery", "title": "Error Recovery & Rollback Process", "description": "Procedures for handling implementation failures, errors, and rollback scenarios", "source": "@site/../../../docs/tech-specs/process/core/error-recovery.mdx", "sourceDirName": "process/core", "slug": "/process/core/error-recovery", "permalink": "/kloudi-swe-agent/tech-specs/process/core/error-recovery", "draft": false, "unlisted": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/core/error-recovery.mdx", "tags": [{"inline": true, "label": "process", "permalink": "/kloudi-swe-agent/tech-specs/tags/process"}, {"inline": true, "label": "error-recovery", "permalink": "/kloudi-swe-agent/tech-specs/tags/error-recovery"}, {"inline": true, "label": "rollback", "permalink": "/kloudi-swe-agent/tech-specs/tags/rollback"}, {"inline": true, "label": "incident-response", "permalink": "/kloudi-swe-agent/tech-specs/tags/incident-response"}], "version": "current", "frontMatter": {"title": "Error Recovery & Rollback Process", "description": "Procedures for handling implementation failures, errors, and rollback scenarios", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "error-recovery", "rollback", "incident-response"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["devops-team", "sre-team"]}}