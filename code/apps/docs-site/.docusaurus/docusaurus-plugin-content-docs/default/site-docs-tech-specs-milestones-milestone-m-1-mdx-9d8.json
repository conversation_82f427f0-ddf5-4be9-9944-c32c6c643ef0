{"id": "milestones/milestone-M1", "title": "Milestone M1 — Static Graph Builder", "description": "Implements static code analysis and JSON-LD graph extraction for Python/JS using Tree-sitter.", "source": "@site/../../../docs/tech-specs/milestones/milestone-M1.mdx", "sourceDirName": "milestones", "slug": "/milestones/milestone-M1", "permalink": "/kloudi-swe-agent/tech-specs/milestones/milestone-M1", "draft": false, "unlisted": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/milestones/milestone-M1.mdx", "tags": [{"inline": true, "label": "milestone", "permalink": "/kloudi-swe-agent/tech-specs/tags/milestone"}], "version": "current", "frontMatter": {"title": "Milestone M1 — Static Graph Builder", "description": "Implements static code analysis and JSON-LD graph extraction for Python/JS using Tree-sitter.", "created": "2024-05-25T00:00:00.000Z", "version": "0.2.0", "status": "Draft", "tags": ["milestone"], "authors": ["nitishMeh<PERSON><PERSON>"]}}