<div class="across-elements-iframes-across" id="across-elements-iframes-across-target">
    <p>
        Lorem ipsum <span>dolor</span> sit amet, consetetur sadipscing elitr, sed
        diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat,
        sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum.
        Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor
        sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
        nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
        diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
        clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.
    </p>
    <p>Lorem ipsum</p>
    <script>
        // Can't use document.write here, as this fixture will be
        // included after the document was fully loaded
        var container = document.getElementById(
            "across-elements-iframes-across-target"
        );
        var insertElement = container.querySelector("p:nth-child(2)");
        var iframe = document.createElement("iframe");
        iframe.src = jasmine.getFixtures().fixturesPath +
            "/across-elements/iframes/nested-inc.html";
        container.insertBefore(iframe, insertElement);
    </script>
</div>
