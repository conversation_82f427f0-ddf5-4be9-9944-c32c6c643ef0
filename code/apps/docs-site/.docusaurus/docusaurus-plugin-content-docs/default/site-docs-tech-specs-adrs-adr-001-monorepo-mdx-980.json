{"id": "adrs/adr-001-monorepo", "title": "ADR-001 — Monorepo Structure with pnpm Workspaces", "description": "Decision to use pnpm workspaces with apps/ and packages/ structure instead of separate repositories.", "source": "@site/../../../docs/tech-specs/adrs/adr-001-monorepo.mdx", "sourceDirName": "adrs", "slug": "/adrs/adr-001-monorepo", "permalink": "/kloudi-swe-agent/tech-specs/adrs/adr-001-monorepo", "draft": false, "unlisted": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/adrs/adr-001-monorepo.mdx", "tags": [{"inline": true, "label": "adr", "permalink": "/kloudi-swe-agent/tech-specs/tags/adr"}, {"inline": true, "label": "architecture", "permalink": "/kloudi-swe-agent/tech-specs/tags/architecture"}, {"inline": true, "label": "monorepo", "permalink": "/kloudi-swe-agent/tech-specs/tags/monorepo"}], "version": "current", "frontMatter": {"title": "ADR-001 — Monorepo Structure with pnpm Workspaces", "description": "Decision to use pnpm workspaces with apps/ and packages/ structure instead of separate repositories.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "monorepo"], "authors": ["nitishMeh<PERSON><PERSON>"]}}