import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/kloudi-swe-agent/blog',
    component: ComponentCreator('/kloudi-swe-agent/blog', 'b6f'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/archive',
    component: ComponentCreator('/kloudi-swe-agent/blog/archive', 'ddf'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/authors',
    component: ComponentCreator('/kloudi-swe-agent/blog/authors', '08c'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/first-blog-post',
    component: ComponentCreator('/kloudi-swe-agent/blog/first-blog-post', 'a1a'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/long-blog-post',
    component: ComponentCreator('/kloudi-swe-agent/blog/long-blog-post', '154'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/mdx-blog-post',
    component: ComponentCreator('/kloudi-swe-agent/blog/mdx-blog-post', 'fb1'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags', '4c9'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags/docusaurus',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags/docusaurus', 'c0b'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags/facebook',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags/facebook', '844'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags/hello',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags/hello', 'd52'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags/hola',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags/hola', '068'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/welcome',
    component: ComponentCreator('/kloudi-swe-agent/blog/welcome', '30e'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/markdown-page',
    component: ComponentCreator('/kloudi-swe-agent/markdown-page', '485'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/search',
    component: ComponentCreator('/kloudi-swe-agent/search', 'e2c'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs', 'ccd'),
    routes: [
      {
        path: '/kloudi-swe-agent/tech-specs',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs', '0df'),
        routes: [
          {
            path: '/kloudi-swe-agent/tech-specs/tags',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags', '889'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/adr',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/adr', '1b5'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/agent-configuration',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/agent-configuration', '15f'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/agent-ready',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/agent-ready', '487'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/agent-rules',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/agent-rules', '9ef'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/agents',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/agents', '289'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/anthropic',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/anthropic', 'f8d'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/architecture',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/architecture', '0f9'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/augment',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/augment', 'dfd'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/branching',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/branching', '7a0'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/checklist',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/checklist', '6c9'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/claude',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/claude', 'd44'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/cleanup',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/cleanup', 'f20'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/configuration',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/configuration', '076'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/copilot',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/copilot', '943'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/core',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/core', '7d1'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/cursor',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/cursor', '873'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/custom',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/custom', '75d'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/data-format',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/data-format', 'ac3'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/decisions',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/decisions', 'dd9'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/documentation',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/documentation', 'd6b'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/docusaurus',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/docusaurus', 'bfb'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/domain',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/domain', '280'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/domain-tag',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/domain-tag', '867'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/error-recovery',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/error-recovery', '344'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/executable',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/executable', '972'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/git',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/git', '3c6'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/github',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/github', 'ea3'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/guide',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/guide', '925'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/hub',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/hub', '7f3'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/implementation',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/implementation', '340'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/incident-response',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/incident-response', '539'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/lessons-learned',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/lessons-learned', '43a'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/maintenance',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/maintenance', '365'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/migration',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/migration', 'ccf'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/milestone',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/milestone', '95a'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/milestones',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/milestones', '879'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/monorepo',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/monorepo', '614'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/navigation',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/navigation', '1e0'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/optimization',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/optimization', 'ce0'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/process',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/process', '315'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/process-improvement',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/process-improvement', '675'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/progress',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/progress', 'd18'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/quality',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/quality', '0e8'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/reference',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/reference', 'f7a'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/requirements',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/requirements', 'da0'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/results',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/results', 'f67'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/rollback',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/rollback', '3d3'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/standards',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/standards', '12d'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/structure',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/structure', '5a1'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/template',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/template', 'e52'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/test',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/test', 'a11'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/testing',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/testing', 'ac3'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/typescript',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/typescript', '275'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/validation',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/validation', '67f'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/work-log',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/work-log', 'e24'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs/tags/workflow',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/workflow', 'ea4'),
            exact: true
          },
          {
            path: '/kloudi-swe-agent/tech-specs',
            component: ComponentCreator('/kloudi-swe-agent/tech-specs', '780'),
            routes: [
              {
                path: '/kloudi-swe-agent/tech-specs/adrs/adr-001-monorepo',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/adr-001-monorepo', '78c'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/adrs/adr-002-typescript',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/adr-002-typescript', 'c82'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/adrs/adr-003-jsonld',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/adr-003-jsonld', '1da'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/adrs/adr-007-docusaurus',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/adr-007-docusaurus', 'd5e'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/adrs/log',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/log', 'c0a'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/dependencies',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/dependencies', '710'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/guides/agent-configuration-guide',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/guides/agent-configuration-guide', 'b3c'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/log',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/log', '04a'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/milestone-M0',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-M0', 'ca0'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/milestone-M0.1',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-M0.1', '6a8'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/milestone-M0.2',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-M0.2', 'afa'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/milestone-M1',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-M1', '389'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/milestone-TEST',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-TEST', '430'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/conversation-summary',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/conversation-summary', 'b02'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/fixes-checklist',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/fixes-checklist', 'e41'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/implementation-log',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/implementation-log', '0b3'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/technical-reference',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/technical-reference', '29e'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/', '756'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/agent-rules/augment',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/augment', 'efc'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/agent-rules/claude',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/claude', '843'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/agent-rules/copilot',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/copilot', 'c73'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/agent-rules/core',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/core', 'e47'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/agent-rules/cursor',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/cursor', '6cb'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/agent-rules/custom',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/custom', '428'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/agent-rules/validation',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/validation', '961'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/core/architectural-decisions',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/architectural-decisions', '0c3'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/core/documentation',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/documentation', '63d'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/core/error-recovery',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/error-recovery', '28d'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/core/git-workflow',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/git-workflow', 'd75'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/core/milestone-implementation',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/milestone-implementation', '9ff'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/core/quality-assurance',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/quality-assurance', 'eac'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/migration-guide',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/migration-guide', '97b'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/optimisation-results/optimisation-result-001-results',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/optimisation-results/optimisation-result-001-results', 'f39'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/templates/adr-template',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/adr-template', '458'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/templates/domain-template',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/domain-template', '4af'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/templates/milestone-template',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/milestone-template', 'e67'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/templates/process-improvement',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/process-improvement', 'e88'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/templates/requirement-checklist',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/requirement-checklist', '3ef'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/process/templates/work-log-template',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/work-log-template', '9fe'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/spec_checklist',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/spec_checklist', 'ab3'),
                exact: true
              },
              {
                path: '/kloudi-swe-agent/tech-specs/structure',
                component: ComponentCreator('/kloudi-swe-agent/tech-specs/structure', '1f9'),
                exact: true,
                sidebar: "techSpecsSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/kloudi-swe-agent/',
    component: ComponentCreator('/kloudi-swe-agent/', 'a70'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
