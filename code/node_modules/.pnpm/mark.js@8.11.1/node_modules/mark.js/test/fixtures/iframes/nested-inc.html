<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test iframe document</title>
    <style>
        .mark {
            background: yellow;
        }
    </style>
    <!-- The above is necessary to check if elements inside <head> will be ignored -->
</head>
<body>
    <div id="iframes-nested-inc-target">
        <p>
            Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor
            invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
            At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd
            gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem
            ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod
            tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
            At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd
            gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.
        </p>
        <script>
            // Can't use document.write here, as this fixture will be
            // included after the document was fully loaded
            var target = document.getElementById("iframes-nested-inc-target");
            var iframe = document.createElement("iframe");
            iframe.src = "inc.html";
            target.appendChild(iframe);
        </script>
    </div>
</body>
</html>
