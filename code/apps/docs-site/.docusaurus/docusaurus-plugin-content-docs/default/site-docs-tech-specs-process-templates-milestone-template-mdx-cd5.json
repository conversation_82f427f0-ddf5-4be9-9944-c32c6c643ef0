{"unversionedId": "process/templates/milestone-template", "id": "process/templates/milestone-template", "title": "Milestone <ID> — <One-line scope>", "description": "<Short paragraph of intent>", "source": "@site/../../../docs/tech-specs/process/templates/milestone-template.mdx", "sourceDirName": "process/templates", "slug": "/process/templates/milestone-template", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/milestone-template", "draft": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/templates/milestone-template.mdx", "tags": [{"label": "milestone", "permalink": "/kloudi-swe-agent/tech-specs/tags/milestone"}], "version": "current", "frontMatter": {"title": "Milestone <ID> — <One-line scope>", "description": "<Short paragraph of intent>", "created": "<YYYY-MM-DD>", "version": "0.0.0", "status": "Draft", "tags": ["milestone"], "authors": []}}