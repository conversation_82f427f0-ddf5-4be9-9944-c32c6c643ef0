hoistPattern:
  - '*'
hoistedDependencies:
  /@adobe/css-tools/4.4.3:
    '@adobe/css-tools': private
  /@algolia/autocomplete-core/1.17.9(@algolia/client-search@5.25.0)(algoliasearch@5.25.0)(search-insights@2.17.3):
    '@algolia/autocomplete-core': private
  /@algolia/autocomplete-plugin-algolia-insights/1.17.9(@algolia/client-search@5.25.0)(algoliasearch@5.25.0)(search-insights@2.17.3):
    '@algolia/autocomplete-plugin-algolia-insights': private
  /@algolia/autocomplete-preset-algolia/1.17.9(@algolia/client-search@5.25.0)(algoliasearch@5.25.0):
    '@algolia/autocomplete-preset-algolia': private
  /@algolia/autocomplete-shared/1.17.9(@algolia/client-search@5.25.0)(algoliasearch@5.25.0):
    '@algolia/autocomplete-shared': private
  /@algolia/client-abtesting/5.25.0:
    '@algolia/client-abtesting': private
  /@algolia/client-analytics/5.25.0:
    '@algolia/client-analytics': private
  /@algolia/client-common/5.25.0:
    '@algolia/client-common': private
  /@algolia/client-insights/5.25.0:
    '@algolia/client-insights': private
  /@algolia/client-personalization/5.25.0:
    '@algolia/client-personalization': private
  /@algolia/client-query-suggestions/5.25.0:
    '@algolia/client-query-suggestions': private
  /@algolia/client-search/5.25.0:
    '@algolia/client-search': private
  /@algolia/events/4.0.1:
    '@algolia/events': private
  /@algolia/ingestion/1.25.0:
    '@algolia/ingestion': private
  /@algolia/monitoring/1.25.0:
    '@algolia/monitoring': private
  /@algolia/recommend/5.25.0:
    '@algolia/recommend': private
  /@algolia/requester-browser-xhr/5.25.0:
    '@algolia/requester-browser-xhr': private
  /@algolia/requester-fetch/5.25.0:
    '@algolia/requester-fetch': private
  /@algolia/requester-node-http/5.25.0:
    '@algolia/requester-node-http': private
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@asamuzakjp/css-color/3.2.0:
    '@asamuzakjp/css-color': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.27.2:
    '@babel/compat-data': private
  /@babel/core/7.27.1:
    '@babel/core': private
  /@babel/generator/7.27.1:
    '@babel/generator': private
  /@babel/helper-annotate-as-pure/7.27.3:
    '@babel/helper-annotate-as-pure': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-create-class-features-plugin/7.27.1(@babel/core@7.27.1):
    '@babel/helper-create-class-features-plugin': private
  /@babel/helper-create-regexp-features-plugin/7.27.1(@babel/core@7.27.1):
    '@babel/helper-create-regexp-features-plugin': private
  /@babel/helper-define-polyfill-provider/0.6.4(@babel/core@7.27.1):
    '@babel/helper-define-polyfill-provider': private
  /@babel/helper-member-expression-to-functions/7.27.1:
    '@babel/helper-member-expression-to-functions': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.1(@babel/core@7.27.1):
    '@babel/helper-module-transforms': private
  /@babel/helper-optimise-call-expression/7.27.1:
    '@babel/helper-optimise-call-expression': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-remap-async-to-generator/7.27.1(@babel/core@7.27.1):
    '@babel/helper-remap-async-to-generator': private
  /@babel/helper-replace-supers/7.27.1(@babel/core@7.27.1):
    '@babel/helper-replace-supers': private
  /@babel/helper-skip-transparent-expression-wrappers/7.27.1:
    '@babel/helper-skip-transparent-expression-wrappers': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helper-wrap-function/7.27.1:
    '@babel/helper-wrap-function': private
  /@babel/helpers/7.27.1:
    '@babel/helpers': private
  /@babel/parser/7.27.2:
    '@babel/parser': private
  /@babel/plugin-bugfix-firefox-class-in-computed-class-key/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  /@babel/plugin-bugfix-safari-class-field-initializer-scope/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  /@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  /@babel/plugin-proposal-private-property-in-object/7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.1):
    '@babel/plugin-proposal-private-property-in-object': private
  /@babel/plugin-syntax-async-generators/7.8.4(@babel/core@7.27.1):
    '@babel/plugin-syntax-async-generators': private
  /@babel/plugin-syntax-bigint/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-bigint': private
  /@babel/plugin-syntax-class-properties/7.12.13(@babel/core@7.27.1):
    '@babel/plugin-syntax-class-properties': private
  /@babel/plugin-syntax-class-static-block/7.14.5(@babel/core@7.27.1):
    '@babel/plugin-syntax-class-static-block': private
  /@babel/plugin-syntax-dynamic-import/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-dynamic-import': private
  /@babel/plugin-syntax-import-assertions/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-syntax-import-assertions': private
  /@babel/plugin-syntax-import-attributes/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-syntax-import-attributes': private
  /@babel/plugin-syntax-import-meta/7.10.4(@babel/core@7.27.1):
    '@babel/plugin-syntax-import-meta': private
  /@babel/plugin-syntax-json-strings/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-json-strings': private
  /@babel/plugin-syntax-jsx/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-syntax-jsx': private
  /@babel/plugin-syntax-logical-assignment-operators/7.10.4(@babel/core@7.27.1):
    '@babel/plugin-syntax-logical-assignment-operators': private
  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  /@babel/plugin-syntax-numeric-separator/7.10.4(@babel/core@7.27.1):
    '@babel/plugin-syntax-numeric-separator': private
  /@babel/plugin-syntax-object-rest-spread/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-object-rest-spread': private
  /@babel/plugin-syntax-optional-catch-binding/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-optional-catch-binding': private
  /@babel/plugin-syntax-optional-chaining/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-optional-chaining': private
  /@babel/plugin-syntax-private-property-in-object/7.14.5(@babel/core@7.27.1):
    '@babel/plugin-syntax-private-property-in-object': private
  /@babel/plugin-syntax-top-level-await/7.14.5(@babel/core@7.27.1):
    '@babel/plugin-syntax-top-level-await': private
  /@babel/plugin-syntax-typescript/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-syntax-typescript': private
  /@babel/plugin-syntax-unicode-sets-regex/7.18.6(@babel/core@7.27.1):
    '@babel/plugin-syntax-unicode-sets-regex': private
  /@babel/plugin-transform-arrow-functions/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-arrow-functions': private
  /@babel/plugin-transform-async-generator-functions/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-async-generator-functions': private
  /@babel/plugin-transform-async-to-generator/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-async-to-generator': private
  /@babel/plugin-transform-block-scoped-functions/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-block-scoped-functions': private
  /@babel/plugin-transform-block-scoping/7.27.3(@babel/core@7.27.1):
    '@babel/plugin-transform-block-scoping': private
  /@babel/plugin-transform-class-properties/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-class-properties': private
  /@babel/plugin-transform-class-static-block/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-class-static-block': private
  /@babel/plugin-transform-classes/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-classes': private
  /@babel/plugin-transform-computed-properties/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-computed-properties': private
  /@babel/plugin-transform-destructuring/7.27.3(@babel/core@7.27.1):
    '@babel/plugin-transform-destructuring': private
  /@babel/plugin-transform-dotall-regex/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-dotall-regex': private
  /@babel/plugin-transform-duplicate-keys/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-duplicate-keys': private
  /@babel/plugin-transform-duplicate-named-capturing-groups-regex/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  /@babel/plugin-transform-dynamic-import/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-dynamic-import': private
  /@babel/plugin-transform-exponentiation-operator/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-exponentiation-operator': private
  /@babel/plugin-transform-export-namespace-from/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-export-namespace-from': private
  /@babel/plugin-transform-for-of/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-for-of': private
  /@babel/plugin-transform-function-name/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-function-name': private
  /@babel/plugin-transform-json-strings/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-json-strings': private
  /@babel/plugin-transform-literals/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-literals': private
  /@babel/plugin-transform-logical-assignment-operators/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-logical-assignment-operators': private
  /@babel/plugin-transform-member-expression-literals/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-member-expression-literals': private
  /@babel/plugin-transform-modules-amd/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-modules-amd': private
  /@babel/plugin-transform-modules-commonjs/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-modules-commonjs': private
  /@babel/plugin-transform-modules-systemjs/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-modules-systemjs': private
  /@babel/plugin-transform-modules-umd/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-modules-umd': private
  /@babel/plugin-transform-named-capturing-groups-regex/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-named-capturing-groups-regex': private
  /@babel/plugin-transform-new-target/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-new-target': private
  /@babel/plugin-transform-nullish-coalescing-operator/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-nullish-coalescing-operator': private
  /@babel/plugin-transform-numeric-separator/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-numeric-separator': private
  /@babel/plugin-transform-object-rest-spread/7.27.3(@babel/core@7.27.1):
    '@babel/plugin-transform-object-rest-spread': private
  /@babel/plugin-transform-object-super/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-object-super': private
  /@babel/plugin-transform-optional-catch-binding/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-optional-catch-binding': private
  /@babel/plugin-transform-optional-chaining/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-optional-chaining': private
  /@babel/plugin-transform-parameters/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-parameters': private
  /@babel/plugin-transform-private-methods/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-private-methods': private
  /@babel/plugin-transform-private-property-in-object/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-private-property-in-object': private
  /@babel/plugin-transform-property-literals/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-property-literals': private
  /@babel/plugin-transform-react-constant-elements/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-react-constant-elements': private
  /@babel/plugin-transform-react-display-name/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-react-display-name': private
  /@babel/plugin-transform-react-jsx-development/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-react-jsx-development': private
  /@babel/plugin-transform-react-jsx-self/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-react-jsx-self': private
  /@babel/plugin-transform-react-jsx-source/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-react-jsx-source': private
  /@babel/plugin-transform-react-jsx/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-react-jsx': private
  /@babel/plugin-transform-react-pure-annotations/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-react-pure-annotations': private
  /@babel/plugin-transform-regenerator/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-regenerator': private
  /@babel/plugin-transform-regexp-modifiers/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-regexp-modifiers': private
  /@babel/plugin-transform-reserved-words/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-reserved-words': private
  /@babel/plugin-transform-runtime/7.27.3(@babel/core@7.27.1):
    '@babel/plugin-transform-runtime': private
  /@babel/plugin-transform-shorthand-properties/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-shorthand-properties': private
  /@babel/plugin-transform-spread/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-spread': private
  /@babel/plugin-transform-sticky-regex/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-sticky-regex': private
  /@babel/plugin-transform-template-literals/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-template-literals': private
  /@babel/plugin-transform-typeof-symbol/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-typeof-symbol': private
  /@babel/plugin-transform-typescript/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-typescript': private
  /@babel/plugin-transform-unicode-escapes/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-unicode-escapes': private
  /@babel/plugin-transform-unicode-property-regex/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-unicode-property-regex': private
  /@babel/plugin-transform-unicode-regex/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-unicode-regex': private
  /@babel/plugin-transform-unicode-sets-regex/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-unicode-sets-regex': private
  /@babel/preset-env/7.27.2(@babel/core@7.27.1):
    '@babel/preset-env': private
  /@babel/preset-modules/0.1.6-no-external-plugins(@babel/core@7.27.1):
    '@babel/preset-modules': private
  /@babel/preset-react/7.27.1(@babel/core@7.27.1):
    '@babel/preset-react': private
  /@babel/preset-typescript/7.27.1(@babel/core@7.27.1):
    '@babel/preset-typescript': private
  /@babel/runtime-corejs3/7.27.3:
    '@babel/runtime-corejs3': private
  /@babel/runtime/7.27.1:
    '@babel/runtime': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.27.1:
    '@babel/traverse': private
  /@babel/types/7.27.1:
    '@babel/types': private
  /@bcoe/v8-coverage/1.0.2:
    '@bcoe/v8-coverage': private
  /@colors/colors/1.5.0:
    '@colors/colors': private
  /@csstools/cascade-layer-name-parser/2.0.5(@csstools/css-parser-algorithms@3.0.5)(@csstools/css-tokenizer@3.0.4):
    '@csstools/cascade-layer-name-parser': private
  /@csstools/color-helpers/5.0.2:
    '@csstools/color-helpers': private
  /@csstools/css-calc/2.1.3(@csstools/css-parser-algorithms@3.0.4)(@csstools/css-tokenizer@3.0.3):
    '@csstools/css-calc': private
  /@csstools/css-color-parser/3.0.9(@csstools/css-parser-algorithms@3.0.4)(@csstools/css-tokenizer@3.0.3):
    '@csstools/css-color-parser': private
  /@csstools/css-parser-algorithms/3.0.4(@csstools/css-tokenizer@3.0.3):
    '@csstools/css-parser-algorithms': private
  /@csstools/css-tokenizer/3.0.3:
    '@csstools/css-tokenizer': private
  /@csstools/media-query-list-parser/4.0.3(@csstools/css-parser-algorithms@3.0.5)(@csstools/css-tokenizer@3.0.4):
    '@csstools/media-query-list-parser': private
  /@csstools/postcss-cascade-layers/5.0.1(postcss@8.5.3):
    '@csstools/postcss-cascade-layers': private
  /@csstools/postcss-color-function/4.0.10(postcss@8.5.3):
    '@csstools/postcss-color-function': private
  /@csstools/postcss-color-mix-function/3.0.10(postcss@8.5.3):
    '@csstools/postcss-color-mix-function': private
  /@csstools/postcss-color-mix-variadic-function-arguments/1.0.0(postcss@8.5.3):
    '@csstools/postcss-color-mix-variadic-function-arguments': private
  /@csstools/postcss-content-alt-text/2.0.6(postcss@8.5.3):
    '@csstools/postcss-content-alt-text': private
  /@csstools/postcss-exponential-functions/2.0.9(postcss@8.5.3):
    '@csstools/postcss-exponential-functions': private
  /@csstools/postcss-font-format-keywords/4.0.0(postcss@8.5.3):
    '@csstools/postcss-font-format-keywords': private
  /@csstools/postcss-gamut-mapping/2.0.10(postcss@8.5.3):
    '@csstools/postcss-gamut-mapping': private
  /@csstools/postcss-gradients-interpolation-method/5.0.10(postcss@8.5.3):
    '@csstools/postcss-gradients-interpolation-method': private
  /@csstools/postcss-hwb-function/4.0.10(postcss@8.5.3):
    '@csstools/postcss-hwb-function': private
  /@csstools/postcss-ic-unit/4.0.2(postcss@8.5.3):
    '@csstools/postcss-ic-unit': private
  /@csstools/postcss-initial/2.0.1(postcss@8.5.3):
    '@csstools/postcss-initial': private
  /@csstools/postcss-is-pseudo-class/5.0.1(postcss@8.5.3):
    '@csstools/postcss-is-pseudo-class': private
  /@csstools/postcss-light-dark-function/2.0.9(postcss@8.5.3):
    '@csstools/postcss-light-dark-function': private
  /@csstools/postcss-logical-float-and-clear/3.0.0(postcss@8.5.3):
    '@csstools/postcss-logical-float-and-clear': private
  /@csstools/postcss-logical-overflow/2.0.0(postcss@8.5.3):
    '@csstools/postcss-logical-overflow': private
  /@csstools/postcss-logical-overscroll-behavior/2.0.0(postcss@8.5.3):
    '@csstools/postcss-logical-overscroll-behavior': private
  /@csstools/postcss-logical-resize/3.0.0(postcss@8.5.3):
    '@csstools/postcss-logical-resize': private
  /@csstools/postcss-logical-viewport-units/3.0.4(postcss@8.5.3):
    '@csstools/postcss-logical-viewport-units': private
  /@csstools/postcss-media-minmax/2.0.9(postcss@8.5.3):
    '@csstools/postcss-media-minmax': private
  /@csstools/postcss-media-queries-aspect-ratio-number-values/3.0.5(postcss@8.5.3):
    '@csstools/postcss-media-queries-aspect-ratio-number-values': private
  /@csstools/postcss-nested-calc/4.0.0(postcss@8.5.3):
    '@csstools/postcss-nested-calc': private
  /@csstools/postcss-normalize-display-values/4.0.0(postcss@8.5.3):
    '@csstools/postcss-normalize-display-values': private
  /@csstools/postcss-oklab-function/4.0.10(postcss@8.5.3):
    '@csstools/postcss-oklab-function': private
  /@csstools/postcss-progressive-custom-properties/4.1.0(postcss@8.5.3):
    '@csstools/postcss-progressive-custom-properties': private
  /@csstools/postcss-random-function/2.0.1(postcss@8.5.3):
    '@csstools/postcss-random-function': private
  /@csstools/postcss-relative-color-syntax/3.0.10(postcss@8.5.3):
    '@csstools/postcss-relative-color-syntax': private
  /@csstools/postcss-scope-pseudo-class/4.0.1(postcss@8.5.3):
    '@csstools/postcss-scope-pseudo-class': private
  /@csstools/postcss-sign-functions/1.1.4(postcss@8.5.3):
    '@csstools/postcss-sign-functions': private
  /@csstools/postcss-stepped-value-functions/4.0.9(postcss@8.5.3):
    '@csstools/postcss-stepped-value-functions': private
  /@csstools/postcss-text-decoration-shorthand/4.0.2(postcss@8.5.3):
    '@csstools/postcss-text-decoration-shorthand': private
  /@csstools/postcss-trigonometric-functions/4.0.9(postcss@8.5.3):
    '@csstools/postcss-trigonometric-functions': private
  /@csstools/postcss-unset-value/4.0.0(postcss@8.5.3):
    '@csstools/postcss-unset-value': private
  /@csstools/selector-resolve-nested/3.0.0(postcss-selector-parser@7.1.0):
    '@csstools/selector-resolve-nested': private
  /@csstools/selector-specificity/5.0.0(postcss-selector-parser@7.1.0):
    '@csstools/selector-specificity': private
  /@csstools/utilities/2.0.0(postcss@8.5.3):
    '@csstools/utilities': private
  /@discoveryjs/json-ext/0.5.7:
    '@discoveryjs/json-ext': private
  /@docsearch/css/3.9.0:
    '@docsearch/css': private
  /@docsearch/react/3.9.0(@algolia/client-search@5.25.0)(@types/react@18.2.79)(react-dom@18.2.0)(react@18.2.0)(search-insights@2.17.3):
    '@docsearch/react': private
  /@docusaurus/babel/3.8.0(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0):
    '@docusaurus/babel': private
  /@docusaurus/bundler/3.8.0(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/bundler': private
  /@docusaurus/core/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(debug@4.4.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/core': private
  /@docusaurus/cssnano-preset/3.8.0:
    '@docusaurus/cssnano-preset': private
  /@docusaurus/logger/3.8.0:
    '@docusaurus/logger': private
  /@docusaurus/mdx-loader/3.8.0(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0):
    '@docusaurus/mdx-loader': private
  /@docusaurus/module-type-aliases/3.8.0(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0):
    '@docusaurus/module-type-aliases': private
  /@docusaurus/plugin-content-blog/3.8.0(@docusaurus/plugin-content-docs@3.8.0)(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-content-blog': private
  /@docusaurus/plugin-content-docs/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(debug@4.4.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-content-docs': private
  /@docusaurus/plugin-content-pages/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-content-pages': private
  /@docusaurus/plugin-css-cascade-layers/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-css-cascade-layers': private
  /@docusaurus/plugin-debug/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-debug': private
  /@docusaurus/plugin-google-analytics/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-google-analytics': private
  /@docusaurus/plugin-google-gtag/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-google-gtag': private
  /@docusaurus/plugin-google-tag-manager/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-google-tag-manager': private
  /@docusaurus/plugin-sitemap/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-sitemap': private
  /@docusaurus/plugin-svgr/3.8.0(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/plugin-svgr': private
  /@docusaurus/preset-classic/3.8.0(@algolia/client-search@5.25.0)(@mdx-js/react@3.1.0)(@types/react@18.2.79)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(search-insights@2.17.3)(typescript@5.4.3):
    '@docusaurus/preset-classic': private
  /@docusaurus/react-loadable/6.0.0(react@18.2.0):
    react-loadable: private
  /@docusaurus/theme-classic/3.8.0(@types/react@18.2.79)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@docusaurus/theme-classic': private
  /@docusaurus/theme-common/3.8.0(@docusaurus/plugin-content-docs@3.8.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0):
    '@docusaurus/theme-common': private
  /@docusaurus/theme-search-algolia/3.8.0(@algolia/client-search@5.25.0)(@mdx-js/react@3.1.0)(@types/react@18.2.79)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(search-insights@2.17.3)(typescript@5.4.3):
    '@docusaurus/theme-search-algolia': private
  /@docusaurus/theme-translations/3.8.0:
    '@docusaurus/theme-translations': private
  /@docusaurus/types/3.8.0(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0):
    '@docusaurus/types': private
  /@docusaurus/utils-common/3.8.0(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0):
    '@docusaurus/utils-common': private
  /@docusaurus/utils-validation/3.8.0(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0):
    '@docusaurus/utils-validation': private
  /@docusaurus/utils/3.8.0(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0):
    '@docusaurus/utils': private
  /@easyops-cn/autocomplete.js/0.38.1:
    '@easyops-cn/autocomplete.js': private
  /@easyops-cn/docusaurus-search-local/0.44.6(@docusaurus/theme-common@3.8.0)(@mdx-js/react@3.1.0)(acorn@8.14.1)(esbuild@0.19.12)(react-dom@18.2.0)(react@18.2.0)(typescript@5.4.3):
    '@easyops-cn/docusaurus-search-local': private
  /@esbuild/aix-ppc64/0.19.12:
    '@esbuild/aix-ppc64': private
  /@esbuild/android-arm/0.19.12:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.19.12:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.19.12:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.19.12:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.19.12:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.19.12:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.19.12:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.19.12:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.19.12:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.19.12:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.19.12:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.19.12:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.19.12:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.19.12:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.19.12:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.19.12:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-x64/0.19.12:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-x64/0.19.12:
    '@esbuild/openbsd-x64': private
  /@esbuild/sunos-x64/0.19.12:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.19.12:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.19.12:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.19.12:
    '@esbuild/win32-x64': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.56.0):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.56.0:
    '@eslint/js': public
  /@hapi/hoek/9.3.0:
    '@hapi/hoek': private
  /@hapi/topo/5.1.0:
    '@hapi/topo': private
  /@humanwhocodes/config-array/0.11.14:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@istanbuljs/load-nyc-config/1.1.0:
    '@istanbuljs/load-nyc-config': private
  /@istanbuljs/schema/0.1.3:
    '@istanbuljs/schema': private
  /@jest/console/29.7.0:
    '@jest/console': private
  /@jest/core/29.7.0:
    '@jest/core': private
  /@jest/environment/29.7.0:
    '@jest/environment': private
  /@jest/expect-utils/29.7.0:
    '@jest/expect-utils': private
  /@jest/expect/29.7.0:
    '@jest/expect': private
  /@jest/fake-timers/29.7.0:
    '@jest/fake-timers': private
  /@jest/globals/29.7.0:
    '@jest/globals': private
  /@jest/reporters/29.7.0:
    '@jest/reporters': private
  /@jest/schemas/29.6.3:
    '@jest/schemas': private
  /@jest/source-map/29.6.3:
    '@jest/source-map': private
  /@jest/test-result/29.7.0:
    '@jest/test-result': private
  /@jest/test-sequencer/29.7.0:
    '@jest/test-sequencer': private
  /@jest/transform/29.7.0:
    '@jest/transform': private
  /@jest/types/29.6.3:
    '@jest/types': private
  /@jridgewell/gen-mapping/0.3.8:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/set-array/1.2.1:
    '@jridgewell/set-array': private
  /@jridgewell/source-map/0.3.6:
    '@jridgewell/source-map': private
  /@jridgewell/sourcemap-codec/1.5.0:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.25:
    '@jridgewell/trace-mapping': private
  /@leichtgewicht/ip-codec/2.0.5:
    '@leichtgewicht/ip-codec': private
  /@mdx-js/mdx/3.1.0(acorn@8.14.1):
    '@mdx-js/mdx': private
  /@mdx-js/react/3.1.0(@types/react@18.2.79)(react@18.2.0):
    '@mdx-js/react': private
  /@noble/hashes/1.8.0:
    '@noble/hashes': private
  /@node-rs/jieba-android-arm-eabi/1.10.4:
    '@node-rs/jieba-android-arm-eabi': private
  /@node-rs/jieba-android-arm64/1.10.4:
    '@node-rs/jieba-android-arm64': private
  /@node-rs/jieba-darwin-arm64/1.10.4:
    '@node-rs/jieba-darwin-arm64': private
  /@node-rs/jieba-darwin-x64/1.10.4:
    '@node-rs/jieba-darwin-x64': private
  /@node-rs/jieba-freebsd-x64/1.10.4:
    '@node-rs/jieba-freebsd-x64': private
  /@node-rs/jieba-linux-arm-gnueabihf/1.10.4:
    '@node-rs/jieba-linux-arm-gnueabihf': private
  /@node-rs/jieba-linux-arm64-gnu/1.10.4:
    '@node-rs/jieba-linux-arm64-gnu': private
  /@node-rs/jieba-linux-arm64-musl/1.10.4:
    '@node-rs/jieba-linux-arm64-musl': private
  /@node-rs/jieba-linux-x64-gnu/1.10.4:
    '@node-rs/jieba-linux-x64-gnu': private
  /@node-rs/jieba-linux-x64-musl/1.10.4:
    '@node-rs/jieba-linux-x64-musl': private
  /@node-rs/jieba-wasm32-wasi/1.10.4:
    '@node-rs/jieba-wasm32-wasi': private
  /@node-rs/jieba-win32-arm64-msvc/1.10.4:
    '@node-rs/jieba-win32-arm64-msvc': private
  /@node-rs/jieba-win32-ia32-msvc/1.10.4:
    '@node-rs/jieba-win32-ia32-msvc': private
  /@node-rs/jieba-win32-x64-msvc/1.10.4:
    '@node-rs/jieba-win32-x64-msvc': private
  /@node-rs/jieba/1.10.4:
    '@node-rs/jieba': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@paralleldrive/cuid2/2.2.2:
    '@paralleldrive/cuid2': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@pkgr/core/0.1.2:
    '@pkgr/core': private
  /@pnpm/config.env-replace/1.1.0:
    '@pnpm/config.env-replace': private
  /@pnpm/network.ca-file/1.0.2:
    '@pnpm/network.ca-file': private
  /@pnpm/npm-conf/2.3.1:
    '@pnpm/npm-conf': private
  /@polka/url/1.0.0-next.29:
    '@polka/url': private
  /@rollup/rollup-android-arm-eabi/4.41.1:
    '@rollup/rollup-android-arm-eabi': private
  /@rollup/rollup-android-arm64/4.41.1:
    '@rollup/rollup-android-arm64': private
  /@rollup/rollup-darwin-arm64/4.41.1:
    '@rollup/rollup-darwin-arm64': private
  /@rollup/rollup-darwin-x64/4.41.1:
    '@rollup/rollup-darwin-x64': private
  /@rollup/rollup-freebsd-arm64/4.41.1:
    '@rollup/rollup-freebsd-arm64': private
  /@rollup/rollup-freebsd-x64/4.41.1:
    '@rollup/rollup-freebsd-x64': private
  /@rollup/rollup-linux-arm-gnueabihf/4.41.1:
    '@rollup/rollup-linux-arm-gnueabihf': private
  /@rollup/rollup-linux-arm-musleabihf/4.41.1:
    '@rollup/rollup-linux-arm-musleabihf': private
  /@rollup/rollup-linux-arm64-gnu/4.41.1:
    '@rollup/rollup-linux-arm64-gnu': private
  /@rollup/rollup-linux-arm64-musl/4.41.1:
    '@rollup/rollup-linux-arm64-musl': private
  /@rollup/rollup-linux-loongarch64-gnu/4.41.1:
    '@rollup/rollup-linux-loongarch64-gnu': private
  /@rollup/rollup-linux-powerpc64le-gnu/4.41.1:
    '@rollup/rollup-linux-powerpc64le-gnu': private
  /@rollup/rollup-linux-riscv64-gnu/4.41.1:
    '@rollup/rollup-linux-riscv64-gnu': private
  /@rollup/rollup-linux-riscv64-musl/4.41.1:
    '@rollup/rollup-linux-riscv64-musl': private
  /@rollup/rollup-linux-s390x-gnu/4.41.1:
    '@rollup/rollup-linux-s390x-gnu': private
  /@rollup/rollup-linux-x64-gnu/4.41.1:
    '@rollup/rollup-linux-x64-gnu': private
  /@rollup/rollup-linux-x64-musl/4.41.1:
    '@rollup/rollup-linux-x64-musl': private
  /@rollup/rollup-win32-arm64-msvc/4.41.1:
    '@rollup/rollup-win32-arm64-msvc': private
  /@rollup/rollup-win32-ia32-msvc/4.41.1:
    '@rollup/rollup-win32-ia32-msvc': private
  /@rollup/rollup-win32-x64-msvc/4.41.1:
    '@rollup/rollup-win32-x64-msvc': private
  /@sideway/address/4.1.5:
    '@sideway/address': private
  /@sideway/formula/3.0.1:
    '@sideway/formula': private
  /@sideway/pinpoint/2.0.0:
    '@sideway/pinpoint': private
  /@sinclair/typebox/0.27.8:
    '@sinclair/typebox': private
  /@sindresorhus/is/4.6.0:
    '@sindresorhus/is': private
  /@sinonjs/commons/3.0.1:
    '@sinonjs/commons': private
  /@sinonjs/fake-timers/10.3.0:
    '@sinonjs/fake-timers': private
  /@slorber/react-helmet-async/1.3.0(react-dom@18.2.0)(react@18.2.0):
    react-helmet-async: private
  /@slorber/remark-comment/1.0.0:
    '@slorber/remark-comment': private
  /@svgr/babel-plugin-add-jsx-attribute/8.0.0(@babel/core@7.27.1):
    '@svgr/babel-plugin-add-jsx-attribute': private
  /@svgr/babel-plugin-remove-jsx-attribute/8.0.0(@babel/core@7.27.1):
    '@svgr/babel-plugin-remove-jsx-attribute': private
  /@svgr/babel-plugin-remove-jsx-empty-expression/8.0.0(@babel/core@7.27.1):
    '@svgr/babel-plugin-remove-jsx-empty-expression': private
  /@svgr/babel-plugin-replace-jsx-attribute-value/8.0.0(@babel/core@7.27.1):
    '@svgr/babel-plugin-replace-jsx-attribute-value': private
  /@svgr/babel-plugin-svg-dynamic-title/8.0.0(@babel/core@7.27.1):
    '@svgr/babel-plugin-svg-dynamic-title': private
  /@svgr/babel-plugin-svg-em-dimensions/8.0.0(@babel/core@7.27.1):
    '@svgr/babel-plugin-svg-em-dimensions': private
  /@svgr/babel-plugin-transform-react-native-svg/8.1.0(@babel/core@7.27.1):
    '@svgr/babel-plugin-transform-react-native-svg': private
  /@svgr/babel-plugin-transform-svg-component/8.0.0(@babel/core@7.27.1):
    '@svgr/babel-plugin-transform-svg-component': private
  /@svgr/babel-preset/8.1.0(@babel/core@7.27.1):
    '@svgr/babel-preset': private
  /@svgr/core/8.1.0(typescript@5.4.3):
    '@svgr/core': private
  /@svgr/hast-util-to-babel-ast/8.0.0:
    '@svgr/hast-util-to-babel-ast': private
  /@svgr/plugin-jsx/8.1.0(@svgr/core@8.1.0):
    '@svgr/plugin-jsx': private
  /@svgr/plugin-svgo/8.1.0(@svgr/core@8.1.0)(typescript@5.4.3):
    '@svgr/plugin-svgo': private
  /@svgr/webpack/8.1.0(typescript@5.4.3):
    '@svgr/webpack': private
  /@szmarczak/http-timer/5.0.1:
    '@szmarczak/http-timer': private
  /@testing-library/dom/9.3.4:
    '@testing-library/dom': private
  /@testing-library/jest-dom/6.4.5(@types/jest@29.5.12)(jest@29.7.0)(vitest@3.1.4):
    '@testing-library/jest-dom': private
  /@testing-library/react/14.3.1(react-dom@18.2.0)(react@18.2.0):
    '@testing-library/react': private
  /@trysound/sax/0.2.0:
    '@trysound/sax': private
  /@tsconfig/docusaurus/1.0.7:
    '@tsconfig/docusaurus': private
  /@types/aria-query/5.0.4:
    '@types/aria-query': private
  /@types/babel__core/7.20.5:
    '@types/babel__core': private
  /@types/babel__generator/7.27.0:
    '@types/babel__generator': private
  /@types/babel__template/7.4.4:
    '@types/babel__template': private
  /@types/babel__traverse/7.20.7:
    '@types/babel__traverse': private
  /@types/body-parser/1.19.5:
    '@types/body-parser': private
  /@types/bonjour/3.5.13:
    '@types/bonjour': private
  /@types/connect-history-api-fallback/1.5.4:
    '@types/connect-history-api-fallback': private
  /@types/connect/3.4.38:
    '@types/connect': private
  /@types/cookiejar/2.1.5:
    '@types/cookiejar': private
  /@types/debug/4.1.12:
    '@types/debug': private
  /@types/eslint-scope/3.7.7:
    '@types/eslint-scope': public
  /@types/eslint/9.6.1:
    '@types/eslint': public
  /@types/estree-jsx/1.0.5:
    '@types/estree-jsx': private
  /@types/estree/1.0.7:
    '@types/estree': private
  /@types/express-serve-static-core/4.19.6:
    '@types/express-serve-static-core': private
  /@types/express/4.17.21:
    '@types/express': private
  /@types/graceful-fs/4.1.9:
    '@types/graceful-fs': private
  /@types/gtag.js/0.0.12:
    '@types/gtag.js': private
  /@types/hast/3.0.4:
    '@types/hast': private
  /@types/history/4.7.11:
    '@types/history': private
  /@types/html-minifier-terser/6.1.0:
    '@types/html-minifier-terser': private
  /@types/http-cache-semantics/4.0.4:
    '@types/http-cache-semantics': private
  /@types/http-errors/2.0.4:
    '@types/http-errors': private
  /@types/http-proxy/1.17.16:
    '@types/http-proxy': private
  /@types/istanbul-lib-coverage/2.0.6:
    '@types/istanbul-lib-coverage': private
  /@types/istanbul-lib-report/3.0.3:
    '@types/istanbul-lib-report': private
  /@types/istanbul-reports/3.0.4:
    '@types/istanbul-reports': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/mdast/4.0.4:
    '@types/mdast': private
  /@types/mdx/2.0.13:
    '@types/mdx': private
  /@types/methods/1.1.4:
    '@types/methods': private
  /@types/mime/1.3.5:
    '@types/mime': private
  /@types/ms/2.1.0:
    '@types/ms': private
  /@types/node-forge/1.3.11:
    '@types/node-forge': private
  /@types/prismjs/1.26.5:
    '@types/prismjs': private
  /@types/prop-types/15.7.14:
    '@types/prop-types': private
  /@types/qs/6.14.0:
    '@types/qs': private
  /@types/range-parser/1.2.7:
    '@types/range-parser': private
  /@types/react-dom/18.2.25:
    '@types/react-dom': private
  /@types/react-router-config/5.0.11:
    '@types/react-router-config': private
  /@types/react-router-dom/5.3.3:
    '@types/react-router-dom': private
  /@types/react-router/5.1.20:
    '@types/react-router': private
  /@types/react/18.2.79:
    '@types/react': private
  /@types/retry/0.12.0:
    '@types/retry': private
  /@types/sax/1.2.7:
    '@types/sax': private
  /@types/semver/7.7.0:
    '@types/semver': private
  /@types/send/0.17.4:
    '@types/send': private
  /@types/serve-index/1.9.4:
    '@types/serve-index': private
  /@types/serve-static/1.15.7:
    '@types/serve-static': private
  /@types/sockjs/0.3.36:
    '@types/sockjs': private
  /@types/stack-utils/2.0.3:
    '@types/stack-utils': private
  /@types/superagent/8.1.9:
    '@types/superagent': private
  /@types/unist/2.0.11:
    '@types/unist': private
  /@types/ws/8.18.1:
    '@types/ws': private
  /@types/yargs-parser/21.0.3:
    '@types/yargs-parser': private
  /@types/yargs/17.0.33:
    '@types/yargs': private
  /@typescript-eslint/scope-manager/7.7.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/type-utils/7.7.0(eslint@8.56.0)(typescript@5.4.3):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/7.7.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/7.7.0(typescript@5.4.3):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/7.7.0(eslint@8.56.0)(typescript@5.4.3):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/7.7.0:
    '@typescript-eslint/visitor-keys': public
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /@vitejs/plugin-react/4.2.1(vite@5.2.2):
    '@vitejs/plugin-react': private
  /@vitest/expect/3.1.4:
    '@vitest/expect': private
  /@vitest/mocker/3.1.4(vite@5.2.2):
    '@vitest/mocker': private
  /@vitest/pretty-format/3.1.4:
    '@vitest/pretty-format': private
  /@vitest/runner/3.1.4:
    '@vitest/runner': private
  /@vitest/snapshot/3.1.4:
    '@vitest/snapshot': private
  /@vitest/spy/3.1.4:
    '@vitest/spy': private
  /@vitest/utils/3.1.4:
    '@vitest/utils': private
  /@webassemblyjs/ast/1.14.1:
    '@webassemblyjs/ast': private
  /@webassemblyjs/floating-point-hex-parser/1.13.2:
    '@webassemblyjs/floating-point-hex-parser': private
  /@webassemblyjs/helper-api-error/1.13.2:
    '@webassemblyjs/helper-api-error': private
  /@webassemblyjs/helper-buffer/1.14.1:
    '@webassemblyjs/helper-buffer': private
  /@webassemblyjs/helper-numbers/1.13.2:
    '@webassemblyjs/helper-numbers': private
  /@webassemblyjs/helper-wasm-bytecode/1.13.2:
    '@webassemblyjs/helper-wasm-bytecode': private
  /@webassemblyjs/helper-wasm-section/1.14.1:
    '@webassemblyjs/helper-wasm-section': private
  /@webassemblyjs/ieee754/1.13.2:
    '@webassemblyjs/ieee754': private
  /@webassemblyjs/leb128/1.13.2:
    '@webassemblyjs/leb128': private
  /@webassemblyjs/utf8/1.13.2:
    '@webassemblyjs/utf8': private
  /@webassemblyjs/wasm-edit/1.14.1:
    '@webassemblyjs/wasm-edit': private
  /@webassemblyjs/wasm-gen/1.14.1:
    '@webassemblyjs/wasm-gen': private
  /@webassemblyjs/wasm-opt/1.14.1:
    '@webassemblyjs/wasm-opt': private
  /@webassemblyjs/wasm-parser/1.14.1:
    '@webassemblyjs/wasm-parser': private
  /@webassemblyjs/wast-printer/1.14.1:
    '@webassemblyjs/wast-printer': private
  /@xtuc/ieee754/1.2.0:
    '@xtuc/ieee754': private
  /@xtuc/long/4.2.2:
    '@xtuc/long': private
  /accepts/1.3.8:
    accepts: private
  /acorn-jsx/5.3.2(acorn@8.14.1):
    acorn-jsx: private
  /acorn-walk/8.3.4:
    acorn-walk: private
  /acorn/8.14.1:
    acorn: private
  /address/1.2.2:
    address: private
  /agent-base/7.1.3:
    agent-base: private
  /aggregate-error/3.1.0:
    aggregate-error: private
  /ajv-formats/2.1.1(ajv@8.17.1):
    ajv-formats: private
  /ajv-keywords/5.1.0(ajv@8.17.1):
    ajv-keywords: private
  /ajv/6.12.6:
    ajv: private
  /algoliasearch-helper/3.25.0(algoliasearch@5.25.0):
    algoliasearch-helper: private
  /algoliasearch/5.25.0:
    algoliasearch: private
  /ansi-align/3.0.1:
    ansi-align: private
  /ansi-escapes/4.3.2:
    ansi-escapes: private
  /ansi-html-community/0.0.8:
    ansi-html-community: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /arg/5.0.2:
    arg: private
  /argparse/1.0.10:
    argparse: private
  /aria-query/5.3.2:
    aria-query: private
  /array-buffer-byte-length/1.0.2:
    array-buffer-byte-length: private
  /array-flatten/1.1.1:
    array-flatten: private
  /array-union/2.1.0:
    array-union: private
  /asap/2.0.6:
    asap: private
  /assertion-error/2.0.1:
    assertion-error: private
  /astring/1.9.0:
    astring: private
  /asynckit/0.4.0:
    asynckit: private
  /autoprefixer/10.4.21(postcss@8.5.3):
    autoprefixer: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /babel-jest/29.7.0(@babel/core@7.27.1):
    babel-jest: private
  /babel-loader/9.2.1(@babel/core@7.27.1)(webpack@5.99.9):
    babel-loader: private
  /babel-plugin-dynamic-import-node/2.3.3:
    babel-plugin-dynamic-import-node: private
  /babel-plugin-istanbul/6.1.1:
    babel-plugin-istanbul: private
  /babel-plugin-jest-hoist/29.6.3:
    babel-plugin-jest-hoist: private
  /babel-plugin-polyfill-corejs2/0.4.13(@babel/core@7.27.1):
    babel-plugin-polyfill-corejs2: private
  /babel-plugin-polyfill-corejs3/0.11.1(@babel/core@7.27.1):
    babel-plugin-polyfill-corejs3: private
  /babel-plugin-polyfill-regenerator/0.6.4(@babel/core@7.27.1):
    babel-plugin-polyfill-regenerator: private
  /babel-preset-current-node-syntax/1.1.0(@babel/core@7.27.1):
    babel-preset-current-node-syntax: private
  /babel-preset-jest/29.6.3(@babel/core@7.27.1):
    babel-preset-jest: private
  /bail/2.0.2:
    bail: private
  /balanced-match/1.0.2:
    balanced-match: private
  /batch/0.6.1:
    batch: private
  /big.js/5.2.2:
    big.js: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /body-parser/1.20.2:
    body-parser: private
  /bonjour-service/1.3.0:
    bonjour-service: private
  /boolbase/1.0.0:
    boolbase: private
  /boxen/6.2.1:
    boxen: private
  /brace-expansion/1.1.11:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.24.5:
    browserslist: private
  /bs-logger/0.2.6:
    bs-logger: private
  /bser/2.1.1:
    bser: private
  /buffer-from/1.1.2:
    buffer-from: private
  /bundle-require/4.2.1(esbuild@0.19.12):
    bundle-require: private
  /bytes/3.1.2:
    bytes: private
  /cac/6.7.14:
    cac: private
  /cacheable-lookup/7.0.0:
    cacheable-lookup: private
  /cacheable-request/10.2.14:
    cacheable-request: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camel-case/4.1.2:
    camel-case: private
  /camelcase/6.3.0:
    camelcase: private
  /caniuse-api/3.0.0:
    caniuse-api: private
  /caniuse-lite/1.0.30001718:
    caniuse-lite: private
  /ccount/2.0.1:
    ccount: private
  /chai/5.2.0:
    chai: private
  /chalk/4.1.2:
    chalk: private
  /char-regex/1.0.2:
    char-regex: private
  /character-entities-html4/2.1.0:
    character-entities-html4: private
  /character-entities-legacy/3.0.0:
    character-entities-legacy: private
  /character-entities/2.0.2:
    character-entities: private
  /character-reference-invalid/2.0.1:
    character-reference-invalid: private
  /check-error/2.1.1:
    check-error: private
  /cheerio-select/2.1.0:
    cheerio-select: private
  /cheerio/1.0.0:
    cheerio: private
  /chokidar/3.6.0:
    chokidar: private
  /chrome-trace-event/1.0.4:
    chrome-trace-event: private
  /ci-info/3.9.0:
    ci-info: private
  /cjs-module-lexer/1.4.3:
    cjs-module-lexer: private
  /clean-css/5.3.3:
    clean-css: private
  /clean-stack/2.2.0:
    clean-stack: private
  /cli-boxes/3.0.0:
    cli-boxes: private
  /cli-cursor/5.0.0:
    cli-cursor: private
  /cli-table3/0.6.5:
    cli-table3: private
  /cli-truncate/4.0.0:
    cli-truncate: private
  /cliui/8.0.1:
    cliui: private
  /clone-deep/4.0.1:
    clone-deep: private
  /clsx/2.1.1:
    clsx: private
  /co/4.6.0:
    co: private
  /collapse-white-space/2.1.0:
    collapse-white-space: private
  /collect-v8-coverage/1.0.2:
    collect-v8-coverage: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /colord/2.9.3:
    colord: private
  /colorette/2.0.20:
    colorette: private
  /combine-promises/1.2.0:
    combine-promises: private
  /combined-stream/1.0.8:
    combined-stream: private
  /comma-separated-tokens/2.0.3:
    comma-separated-tokens: private
  /commander/5.1.0:
    commander: private
  /common-path-prefix/3.0.0:
    common-path-prefix: private
  /component-emitter/1.3.1:
    component-emitter: private
  /compressible/2.0.18:
    compressible: private
  /compression/1.8.0:
    compression: private
  /concat-map/0.0.1:
    concat-map: private
  /config-chain/1.1.13:
    config-chain: private
  /configstore/6.0.0:
    configstore: private
  /connect-history-api-fallback/2.0.0:
    connect-history-api-fallback: private
  /consola/3.4.2:
    consola: private
  /content-disposition/0.5.4:
    content-disposition: private
  /content-type/1.0.5:
    content-type: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cookie-signature/1.0.6:
    cookie-signature: private
  /cookie/0.6.0:
    cookie: private
  /cookiejar/2.1.4:
    cookiejar: private
  /copy-text-to-clipboard/3.2.0:
    copy-text-to-clipboard: private
  /copy-webpack-plugin/11.0.0(webpack@5.99.9):
    copy-webpack-plugin: private
  /core-js-compat/3.42.0:
    core-js-compat: private
  /core-js-pure/3.42.0:
    core-js-pure: private
  /core-js/3.42.0:
    core-js: private
  /core-util-is/1.0.3:
    core-util-is: private
  /cosmiconfig/8.3.6(typescript@5.4.3):
    cosmiconfig: private
  /create-jest/29.7.0(@types/node@20.12.7):
    create-jest: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /crypto-random-string/4.0.0:
    crypto-random-string: private
  /css-blank-pseudo/7.0.1(postcss@8.5.3):
    css-blank-pseudo: private
  /css-declaration-sorter/7.2.0(postcss@8.5.3):
    css-declaration-sorter: private
  /css-has-pseudo/7.0.2(postcss@8.5.3):
    css-has-pseudo: private
  /css-loader/6.11.0(webpack@5.99.9):
    css-loader: private
  /css-minimizer-webpack-plugin/5.0.1(clean-css@5.3.3)(esbuild@0.19.12)(webpack@5.99.9):
    css-minimizer-webpack-plugin: private
  /css-prefers-color-scheme/10.0.0(postcss@8.5.3):
    css-prefers-color-scheme: private
  /css-select/5.1.0:
    css-select: private
  /css-tree/2.3.1:
    css-tree: private
  /css-what/6.1.0:
    css-what: private
  /css.escape/1.5.1:
    css.escape: private
  /cssdb/8.3.0:
    cssdb: private
  /cssesc/3.0.0:
    cssesc: private
  /cssnano-preset-advanced/6.1.2(postcss@8.5.3):
    cssnano-preset-advanced: private
  /cssnano-preset-default/6.1.2(postcss@8.5.3):
    cssnano-preset-default: private
  /cssnano-utils/4.0.2(postcss@8.5.3):
    cssnano-utils: private
  /cssnano/6.1.2(postcss@8.5.3):
    cssnano: private
  /csso/5.0.5:
    csso: private
  /cssstyle/4.3.1:
    cssstyle: private
  /csstype/3.1.3:
    csstype: private
  /data-urls/5.0.0:
    data-urls: private
  /debounce/1.2.1:
    debounce: private
  /debug/4.4.1(supports-color@5.5.0):
    debug: private
  /decimal.js/10.5.0:
    decimal.js: private
  /decode-named-character-reference/1.1.0:
    decode-named-character-reference: private
  /decompress-response/6.0.0:
    decompress-response: private
  /dedent/1.6.0:
    dedent: private
  /deep-eql/5.0.2:
    deep-eql: private
  /deep-equal/2.2.3:
    deep-equal: private
  /deep-extend/0.6.0:
    deep-extend: private
  /deep-is/0.1.4:
    deep-is: private
  /deepmerge/4.3.1:
    deepmerge: private
  /default-gateway/6.0.3:
    default-gateway: private
  /defer-to-connect/2.0.1:
    defer-to-connect: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-lazy-prop/2.0.0:
    define-lazy-prop: private
  /define-properties/1.2.1:
    define-properties: private
  /delayed-stream/1.0.0:
    delayed-stream: private
  /depd/2.0.0:
    depd: private
  /dequal/2.0.3:
    dequal: private
  /destroy/1.2.0:
    destroy: private
  /detect-newline/3.1.0:
    detect-newline: private
  /detect-node/2.1.0:
    detect-node: private
  /detect-port/1.6.1:
    detect-port: private
  /devlop/1.1.0:
    devlop: private
  /dezalgo/1.0.4:
    dezalgo: private
  /diff-sequences/29.6.3:
    diff-sequences: private
  /dir-glob/3.0.1:
    dir-glob: private
  /dns-packet/5.6.1:
    dns-packet: private
  /doctrine/3.0.0:
    doctrine: private
  /dom-accessibility-api/0.6.3:
    dom-accessibility-api: private
  /dom-converter/0.2.0:
    dom-converter: private
  /dom-serializer/2.0.0:
    dom-serializer: private
  /domelementtype/2.3.0:
    domelementtype: private
  /domhandler/5.0.3:
    domhandler: private
  /domutils/3.2.2:
    domutils: private
  /dot-case/3.0.4:
    dot-case: private
  /dot-prop/6.0.1:
    dot-prop: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /duplexer/0.1.2:
    duplexer: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /ee-first/1.1.1:
    ee-first: private
  /electron-to-chromium/1.5.157:
    electron-to-chromium: private
  /emittery/0.13.1:
    emittery: private
  /emoji-regex/10.4.0:
    emoji-regex: private
  /emojilib/2.4.0:
    emojilib: private
  /emojis-list/3.0.0:
    emojis-list: private
  /emoticon/4.1.0:
    emoticon: private
  /encodeurl/1.0.2:
    encodeurl: private
  /encoding-sniffer/0.2.0:
    encoding-sniffer: private
  /enhanced-resolve/5.18.1:
    enhanced-resolve: private
  /entities/6.0.0:
    entities: private
  /environment/1.1.0:
    environment: private
  /error-ex/1.3.2:
    error-ex: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-get-iterator/1.1.3:
    es-get-iterator: private
  /es-module-lexer/1.7.0:
    es-module-lexer: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /esast-util-from-estree/2.0.0:
    esast-util-from-estree: private
  /esast-util-from-js/2.0.1:
    esast-util-from-js: private
  /esbuild/0.19.12:
    esbuild: private
  /escalade/3.2.0:
    escalade: private
  /escape-goat/4.0.0:
    escape-goat: private
  /escape-html/1.0.3:
    escape-html: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /espree/9.6.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /estree-util-attach-comments/3.0.0:
    estree-util-attach-comments: private
  /estree-util-build-jsx/3.0.1:
    estree-util-build-jsx: private
  /estree-util-is-identifier-name/3.0.0:
    estree-util-is-identifier-name: private
  /estree-util-scope/1.0.0:
    estree-util-scope: private
  /estree-util-to-js/2.0.0:
    estree-util-to-js: private
  /estree-util-value-to-estree/3.4.0:
    estree-util-value-to-estree: private
  /estree-util-visit/2.0.0:
    estree-util-visit: private
  /estree-walker/3.0.3:
    estree-walker: private
  /esutils/2.0.3:
    esutils: private
  /eta/2.2.0:
    eta: private
  /etag/1.8.1:
    etag: private
  /eval/0.1.8:
    eval: private
  /eventemitter3/5.0.1:
    eventemitter3: private
  /events/3.3.0:
    events: private
  /execa/5.1.1:
    execa: private
  /exit/0.1.2:
    exit: private
  /expect-type/1.2.1:
    expect-type: private
  /expect/29.7.0:
    expect: private
  /express/4.19.2:
    express: private
  /extend-shallow/2.0.1:
    extend-shallow: private
  /extend/3.0.2:
    extend: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-diff/1.3.0:
    fast-diff: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fast-safe-stringify/2.1.1:
    fast-safe-stringify: private
  /fast-uri/3.0.6:
    fast-uri: private
  /fastq/1.19.1:
    fastq: private
  /fault/2.0.1:
    fault: private
  /faye-websocket/0.11.4:
    faye-websocket: private
  /fb-watchman/2.0.2:
    fb-watchman: private
  /fdir/6.4.4(picomatch@4.0.2):
    fdir: private
  /feed/4.2.2:
    feed: private
  /figures/3.2.0:
    figures: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /file-loader/6.2.0(webpack@5.99.9):
    file-loader: private
  /fill-range/7.1.1:
    fill-range: private
  /finalhandler/1.2.0:
    finalhandler: private
  /find-cache-dir/4.0.0:
    find-cache-dir: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flat/5.0.2:
    flat: private
  /flatted/3.3.3:
    flatted: private
  /follow-redirects/1.15.9(debug@4.4.1):
    follow-redirects: private
  /for-each/0.3.5:
    for-each: private
  /foreground-child/3.3.1:
    foreground-child: private
  /form-data-encoder/2.1.4:
    form-data-encoder: private
  /form-data/4.0.2:
    form-data: private
  /format/0.2.2:
    format: private
  /formidable/3.5.4:
    formidable: private
  /forwarded/0.2.0:
    forwarded: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fresh/0.5.2:
    fresh: private
  /fs-extra/11.3.0:
    fs-extra: private
  /fs-monkey/1.0.6:
    fs-monkey: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /functions-have-names/1.2.3:
    functions-have-names: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-east-asian-width/1.3.0:
    get-east-asian-width: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-own-enumerable-property-symbols/3.0.2:
    get-own-enumerable-property-symbols: private
  /get-package-type/0.1.0:
    get-package-type: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/6.0.1:
    get-stream: private
  /github-slugger/1.5.0:
    github-slugger: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob-to-regexp/0.4.1:
    glob-to-regexp: private
  /glob/10.4.5:
    glob: private
  /global-dirs/3.0.1:
    global-dirs: private
  /globals/13.24.0:
    globals: private
  /globby/11.1.0:
    globby: private
  /gopd/1.2.0:
    gopd: private
  /got/12.6.1:
    got: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /graphemer/1.4.0:
    graphemer: private
  /gzip-size/6.0.0:
    gzip-size: private
  /handle-thing/2.0.1:
    handle-thing: private
  /has-bigints/1.1.0:
    has-bigints: private
  /has-flag/3.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /has-yarn/3.0.0:
    has-yarn: private
  /hasown/2.0.2:
    hasown: private
  /hast-util-from-parse5/8.0.3:
    hast-util-from-parse5: private
  /hast-util-parse-selector/4.0.0:
    hast-util-parse-selector: private
  /hast-util-raw/9.1.0:
    hast-util-raw: private
  /hast-util-to-estree/3.1.3:
    hast-util-to-estree: private
  /hast-util-to-jsx-runtime/2.3.6:
    hast-util-to-jsx-runtime: private
  /hast-util-to-parse5/8.0.0:
    hast-util-to-parse5: private
  /hast-util-whitespace/3.0.0:
    hast-util-whitespace: private
  /hastscript/9.0.1:
    hastscript: private
  /he/1.2.0:
    he: private
  /history/4.10.1:
    history: private
  /hoist-non-react-statics/3.3.2:
    hoist-non-react-statics: private
  /hpack.js/2.1.6:
    hpack.js: private
  /html-encoding-sniffer/4.0.0:
    html-encoding-sniffer: private
  /html-entities/2.6.0:
    html-entities: private
  /html-escaper/2.0.2:
    html-escaper: private
  /html-minifier-terser/7.2.0:
    html-minifier-terser: private
  /html-tags/3.3.1:
    html-tags: private
  /html-void-elements/3.0.0:
    html-void-elements: private
  /html-webpack-plugin/5.6.3(webpack@5.99.9):
    html-webpack-plugin: private
  /htmlparser2/9.1.0:
    htmlparser2: private
  /http-cache-semantics/4.2.0:
    http-cache-semantics: private
  /http-deceiver/1.2.7:
    http-deceiver: private
  /http-errors/2.0.0:
    http-errors: private
  /http-parser-js/0.5.10:
    http-parser-js: private
  /http-proxy-agent/7.0.2:
    http-proxy-agent: private
  /http-proxy-middleware/2.0.9(@types/express@4.17.21)(debug@4.4.1):
    http-proxy-middleware: private
  /http-proxy/1.18.1(debug@4.4.1):
    http-proxy: private
  /http2-wrapper/2.2.1:
    http2-wrapper: private
  /https-proxy-agent/7.0.6:
    https-proxy-agent: private
  /human-signals/2.1.0:
    human-signals: private
  /iconv-lite/0.4.24:
    iconv-lite: private
  /icss-utils/5.1.0(postcss@8.5.3):
    icss-utils: private
  /ignore-by-default/1.0.1:
    ignore-by-default: private
  /ignore/5.3.2:
    ignore: private
  /image-size/2.0.2:
    image-size: private
  /immediate/3.3.0:
    immediate: private
  /import-fresh/3.3.1:
    import-fresh: private
  /import-lazy/4.0.0:
    import-lazy: private
  /import-local/3.2.0:
    import-local: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indent-string/4.0.0:
    indent-string: private
  /infima/0.2.0-alpha.45:
    infima: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ini/2.0.0:
    ini: private
  /inline-style-parser/0.2.4:
    inline-style-parser: private
  /internal-slot/1.1.0:
    internal-slot: private
  /invariant/2.2.4:
    invariant: private
  /ipaddr.js/1.9.1:
    ipaddr.js: private
  /is-alphabetical/2.0.1:
    is-alphabetical: private
  /is-alphanumerical/2.0.1:
    is-alphanumerical: private
  /is-arguments/1.2.0:
    is-arguments: private
  /is-array-buffer/3.0.5:
    is-array-buffer: private
  /is-arrayish/0.2.1:
    is-arrayish: private
  /is-bigint/1.1.0:
    is-bigint: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-boolean-object/1.2.2:
    is-boolean-object: private
  /is-callable/1.2.7:
    is-callable: private
  /is-ci/3.0.1:
    is-ci: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-date-object/1.1.0:
    is-date-object: private
  /is-decimal/2.0.1:
    is-decimal: private
  /is-docker/2.2.1:
    is-docker: private
  /is-extendable/0.1.1:
    is-extendable: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-fullwidth-code-point/4.0.0:
    is-fullwidth-code-point: private
  /is-generator-fn/2.1.0:
    is-generator-fn: private
  /is-glob/4.0.3:
    is-glob: private
  /is-hexadecimal/2.0.1:
    is-hexadecimal: private
  /is-installed-globally/0.4.0:
    is-installed-globally: private
  /is-map/2.0.3:
    is-map: private
  /is-npm/6.0.0:
    is-npm: private
  /is-number-object/1.1.1:
    is-number-object: private
  /is-number/7.0.0:
    is-number: private
  /is-obj/1.0.1:
    is-obj: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-plain-obj/3.0.0:
    is-plain-obj: private
  /is-plain-object/2.0.4:
    is-plain-object: private
  /is-potential-custom-element-name/1.0.1:
    is-potential-custom-element-name: private
  /is-regex/1.2.1:
    is-regex: private
  /is-regexp/1.0.0:
    is-regexp: private
  /is-set/2.0.3:
    is-set: private
  /is-shared-array-buffer/1.0.4:
    is-shared-array-buffer: private
  /is-stream/2.0.1:
    is-stream: private
  /is-string/1.1.1:
    is-string: private
  /is-symbol/1.1.1:
    is-symbol: private
  /is-typedarray/1.0.0:
    is-typedarray: private
  /is-weakmap/2.0.2:
    is-weakmap: private
  /is-weakset/2.0.4:
    is-weakset: private
  /is-wsl/2.2.0:
    is-wsl: private
  /is-yarn-global/0.4.1:
    is-yarn-global: private
  /isarray/0.0.1:
    isarray: private
  /isexe/2.0.0:
    isexe: private
  /isobject/3.0.1:
    isobject: private
  /istanbul-lib-coverage/3.2.2:
    istanbul-lib-coverage: private
  /istanbul-lib-instrument/6.0.3:
    istanbul-lib-instrument: private
  /istanbul-lib-report/3.0.1:
    istanbul-lib-report: private
  /istanbul-lib-source-maps/5.0.6:
    istanbul-lib-source-maps: private
  /istanbul-reports/3.1.7:
    istanbul-reports: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jest-changed-files/29.7.0:
    jest-changed-files: private
  /jest-circus/29.7.0:
    jest-circus: private
  /jest-cli/29.7.0(@types/node@20.12.7):
    jest-cli: private
  /jest-config/29.7.0(@types/node@20.12.7):
    jest-config: private
  /jest-diff/29.7.0:
    jest-diff: private
  /jest-docblock/29.7.0:
    jest-docblock: private
  /jest-each/29.7.0:
    jest-each: private
  /jest-environment-node/29.7.0:
    jest-environment-node: private
  /jest-get-type/29.6.3:
    jest-get-type: private
  /jest-haste-map/29.7.0:
    jest-haste-map: private
  /jest-leak-detector/29.7.0:
    jest-leak-detector: private
  /jest-matcher-utils/29.7.0:
    jest-matcher-utils: private
  /jest-message-util/29.7.0:
    jest-message-util: private
  /jest-mock/29.7.0:
    jest-mock: private
  /jest-pnp-resolver/1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  /jest-regex-util/29.6.3:
    jest-regex-util: private
  /jest-resolve-dependencies/29.7.0:
    jest-resolve-dependencies: private
  /jest-resolve/29.7.0:
    jest-resolve: private
  /jest-runner/29.7.0:
    jest-runner: private
  /jest-runtime/29.7.0:
    jest-runtime: private
  /jest-snapshot/29.7.0:
    jest-snapshot: private
  /jest-util/29.7.0:
    jest-util: private
  /jest-validate/29.7.0:
    jest-validate: private
  /jest-watcher/29.7.0:
    jest-watcher: private
  /jest-worker/29.7.0:
    jest-worker: private
  /jiti/1.21.7:
    jiti: private
  /joi/17.13.3:
    joi: private
  /joycon/3.1.1:
    joycon: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsdom/24.1.0:
    jsdom: private
  /jsesc/3.1.0:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-parse-even-better-errors/2.3.1:
    json-parse-even-better-errors: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json5/2.2.3:
    json5: private
  /jsonfile/6.1.0:
    jsonfile: private
  /keyv/4.5.4:
    keyv: private
  /kind-of/6.0.3:
    kind-of: private
  /klaw-sync/6.0.0:
    klaw-sync: private
  /kleur/3.0.3:
    kleur: private
  /latest-version/7.0.0:
    latest-version: private
  /launch-editor/2.10.0:
    launch-editor: private
  /leven/3.1.0:
    leven: private
  /levn/0.4.1:
    levn: private
  /lilconfig/3.0.0:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /listr2/8.0.1:
    listr2: private
  /load-tsconfig/0.2.5:
    load-tsconfig: private
  /loader-runner/4.3.0:
    loader-runner: private
  /loader-utils/2.0.4:
    loader-utils: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.debounce/4.0.8:
    lodash.debounce: private
  /lodash.memoize/4.1.2:
    lodash.memoize: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.sortby/4.7.0:
    lodash.sortby: private
  /lodash.uniq/4.5.0:
    lodash.uniq: private
  /lodash/4.17.21:
    lodash: private
  /log-update/6.1.0:
    log-update: private
  /longest-streak/3.1.0:
    longest-streak: private
  /loose-envify/1.4.0:
    loose-envify: private
  /loupe/3.1.3:
    loupe: private
  /lower-case/2.0.2:
    lower-case: private
  /lowercase-keys/3.0.0:
    lowercase-keys: private
  /lru-cache/10.4.3:
    lru-cache: private
  /lunr-languages/1.14.0:
    lunr-languages: private
  /lunr/2.3.9:
    lunr: private
  /lz-string/1.5.0:
    lz-string: private
  /magic-string/0.30.17:
    magic-string: private
  /magicast/0.3.5:
    magicast: private
  /make-dir/4.0.0:
    make-dir: private
  /make-error/1.3.6:
    make-error: private
  /makeerror/1.0.12:
    makeerror: private
  /mark.js/8.11.1:
    mark.js: private
  /markdown-extensions/2.0.0:
    markdown-extensions: private
  /markdown-table/2.0.0:
    markdown-table: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /mdast-util-directive/3.1.0:
    mdast-util-directive: private
  /mdast-util-find-and-replace/3.0.2:
    mdast-util-find-and-replace: private
  /mdast-util-from-markdown/2.0.2:
    mdast-util-from-markdown: private
  /mdast-util-frontmatter/2.0.1:
    mdast-util-frontmatter: private
  /mdast-util-gfm-autolink-literal/2.0.1:
    mdast-util-gfm-autolink-literal: private
  /mdast-util-gfm-footnote/2.1.0:
    mdast-util-gfm-footnote: private
  /mdast-util-gfm-strikethrough/2.0.0:
    mdast-util-gfm-strikethrough: private
  /mdast-util-gfm-table/2.0.0:
    mdast-util-gfm-table: private
  /mdast-util-gfm-task-list-item/2.0.0:
    mdast-util-gfm-task-list-item: private
  /mdast-util-gfm/3.1.0:
    mdast-util-gfm: private
  /mdast-util-mdx-expression/2.0.1:
    mdast-util-mdx-expression: private
  /mdast-util-mdx-jsx/3.2.0:
    mdast-util-mdx-jsx: private
  /mdast-util-mdx/3.0.0:
    mdast-util-mdx: private
  /mdast-util-mdxjs-esm/2.0.1:
    mdast-util-mdxjs-esm: private
  /mdast-util-phrasing/4.1.0:
    mdast-util-phrasing: private
  /mdast-util-to-hast/13.2.0:
    mdast-util-to-hast: private
  /mdast-util-to-markdown/2.1.2:
    mdast-util-to-markdown: private
  /mdast-util-to-string/4.0.0:
    mdast-util-to-string: private
  /mdn-data/2.0.30:
    mdn-data: private
  /media-typer/0.3.0:
    media-typer: private
  /memfs/3.5.3:
    memfs: private
  /merge-descriptors/1.0.1:
    merge-descriptors: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /methods/1.1.2:
    methods: private
  /micromark-core-commonmark/2.0.3:
    micromark-core-commonmark: private
  /micromark-extension-directive/3.0.2:
    micromark-extension-directive: private
  /micromark-extension-frontmatter/2.0.0:
    micromark-extension-frontmatter: private
  /micromark-extension-gfm-autolink-literal/2.1.0:
    micromark-extension-gfm-autolink-literal: private
  /micromark-extension-gfm-footnote/2.1.0:
    micromark-extension-gfm-footnote: private
  /micromark-extension-gfm-strikethrough/2.1.0:
    micromark-extension-gfm-strikethrough: private
  /micromark-extension-gfm-table/2.1.1:
    micromark-extension-gfm-table: private
  /micromark-extension-gfm-tagfilter/2.0.0:
    micromark-extension-gfm-tagfilter: private
  /micromark-extension-gfm-task-list-item/2.1.0:
    micromark-extension-gfm-task-list-item: private
  /micromark-extension-gfm/3.0.0:
    micromark-extension-gfm: private
  /micromark-extension-mdx-expression/3.0.1:
    micromark-extension-mdx-expression: private
  /micromark-extension-mdx-jsx/3.0.2:
    micromark-extension-mdx-jsx: private
  /micromark-extension-mdx-md/2.0.0:
    micromark-extension-mdx-md: private
  /micromark-extension-mdxjs-esm/3.0.0:
    micromark-extension-mdxjs-esm: private
  /micromark-extension-mdxjs/3.0.0:
    micromark-extension-mdxjs: private
  /micromark-factory-destination/2.0.1:
    micromark-factory-destination: private
  /micromark-factory-label/2.0.1:
    micromark-factory-label: private
  /micromark-factory-mdx-expression/2.0.3:
    micromark-factory-mdx-expression: private
  /micromark-factory-space/1.1.0:
    micromark-factory-space: private
  /micromark-factory-title/2.0.1:
    micromark-factory-title: private
  /micromark-factory-whitespace/2.0.1:
    micromark-factory-whitespace: private
  /micromark-util-character/1.2.0:
    micromark-util-character: private
  /micromark-util-chunked/2.0.1:
    micromark-util-chunked: private
  /micromark-util-classify-character/2.0.1:
    micromark-util-classify-character: private
  /micromark-util-combine-extensions/2.0.1:
    micromark-util-combine-extensions: private
  /micromark-util-decode-numeric-character-reference/2.0.2:
    micromark-util-decode-numeric-character-reference: private
  /micromark-util-decode-string/2.0.1:
    micromark-util-decode-string: private
  /micromark-util-encode/2.0.1:
    micromark-util-encode: private
  /micromark-util-events-to-acorn/2.0.3:
    micromark-util-events-to-acorn: private
  /micromark-util-html-tag-name/2.0.1:
    micromark-util-html-tag-name: private
  /micromark-util-normalize-identifier/2.0.1:
    micromark-util-normalize-identifier: private
  /micromark-util-resolve-all/2.0.1:
    micromark-util-resolve-all: private
  /micromark-util-sanitize-uri/2.0.1:
    micromark-util-sanitize-uri: private
  /micromark-util-subtokenize/2.1.0:
    micromark-util-subtokenize: private
  /micromark-util-symbol/1.1.0:
    micromark-util-symbol: private
  /micromark-util-types/2.0.2:
    micromark-util-types: private
  /micromark/4.0.2:
    micromark: private
  /micromatch/4.0.5:
    micromatch: private
  /mime-db/1.33.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mime/1.6.0:
    mime: private
  /mimic-fn/2.1.0:
    mimic-fn: private
  /mimic-function/5.0.1:
    mimic-function: private
  /mimic-response/4.0.0:
    mimic-response: private
  /min-indent/1.0.1:
    min-indent: private
  /mini-css-extract-plugin/2.9.2(webpack@5.99.9):
    mini-css-extract-plugin: private
  /minimalistic-assert/1.0.1:
    minimalistic-assert: private
  /minimatch/3.1.2:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /minipass/7.1.2:
    minipass: private
  /mrmime/2.0.1:
    mrmime: private
  /ms/2.0.0:
    ms: private
  /multicast-dns/7.2.5:
    multicast-dns: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /natural-compare/1.4.0:
    natural-compare: private
  /negotiator/0.6.3:
    negotiator: private
  /neo-async/2.6.2:
    neo-async: private
  /no-case/3.0.4:
    no-case: private
  /node-emoji/2.2.0:
    node-emoji: private
  /node-forge/1.3.1:
    node-forge: private
  /node-int64/0.4.0:
    node-int64: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /normalize-url/8.0.1:
    normalize-url: private
  /npm-run-path/4.0.1:
    npm-run-path: private
  /nprogress/0.2.0:
    nprogress: private
  /nth-check/2.1.1:
    nth-check: private
  /null-loader/4.0.1(webpack@5.99.9):
    null-loader: private
  /nwsapi/2.2.20:
    nwsapi: private
  /object-assign/4.1.1:
    object-assign: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-is/1.1.6:
    object-is: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.7:
    object.assign: private
  /obuf/1.1.2:
    obuf: private
  /on-finished/2.4.1:
    on-finished: private
  /on-headers/1.0.2:
    on-headers: private
  /once/1.4.0:
    once: private
  /onetime/5.1.2:
    onetime: private
  /open/8.4.2:
    open: private
  /opener/1.5.2:
    opener: private
  /optionator/0.9.4:
    optionator: private
  /p-cancelable/3.0.0:
    p-cancelable: private
  /p-finally/1.0.0:
    p-finally: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /p-map/4.0.0:
    p-map: private
  /p-queue/6.6.2:
    p-queue: private
  /p-retry/4.6.2:
    p-retry: private
  /p-timeout/3.2.0:
    p-timeout: private
  /p-try/2.2.0:
    p-try: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /package-json/8.1.1:
    package-json: private
  /param-case/3.0.4:
    param-case: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-entities/4.0.2:
    parse-entities: private
  /parse-json/5.2.0:
    parse-json: private
  /parse-numeric-range/1.3.0:
    parse-numeric-range: private
  /parse5-htmlparser2-tree-adapter/7.1.0:
    parse5-htmlparser2-tree-adapter: private
  /parse5-parser-stream/7.1.2:
    parse5-parser-stream: private
  /parse5/7.3.0:
    parse5: private
  /parseurl/1.3.3:
    parseurl: private
  /pascal-case/3.1.2:
    pascal-case: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-is-inside/1.0.2:
    path-is-inside: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /path-to-regexp/0.1.7:
    path-to-regexp: private
  /path-type/4.0.0:
    path-type: private
  /pathe/2.0.3:
    pathe: private
  /pathval/2.0.0:
    pathval: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/2.3.1:
    picomatch: private
  /pidtree/0.6.0:
    pidtree: private
  /pirates/4.0.7:
    pirates: private
  /pkg-dir/4.2.0:
    pkg-dir: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-attribute-case-insensitive/7.0.1(postcss@8.5.3):
    postcss-attribute-case-insensitive: private
  /postcss-calc/9.0.1(postcss@8.5.3):
    postcss-calc: private
  /postcss-clamp/4.1.0(postcss@8.5.3):
    postcss-clamp: private
  /postcss-color-functional-notation/7.0.10(postcss@8.5.3):
    postcss-color-functional-notation: private
  /postcss-color-hex-alpha/10.0.0(postcss@8.5.3):
    postcss-color-hex-alpha: private
  /postcss-color-rebeccapurple/10.0.0(postcss@8.5.3):
    postcss-color-rebeccapurple: private
  /postcss-colormin/6.1.0(postcss@8.5.3):
    postcss-colormin: private
  /postcss-convert-values/6.1.0(postcss@8.5.3):
    postcss-convert-values: private
  /postcss-custom-media/11.0.6(postcss@8.5.3):
    postcss-custom-media: private
  /postcss-custom-properties/14.0.5(postcss@8.5.3):
    postcss-custom-properties: private
  /postcss-custom-selectors/8.0.5(postcss@8.5.3):
    postcss-custom-selectors: private
  /postcss-dir-pseudo-class/9.0.1(postcss@8.5.3):
    postcss-dir-pseudo-class: private
  /postcss-discard-comments/6.0.2(postcss@8.5.3):
    postcss-discard-comments: private
  /postcss-discard-duplicates/6.0.3(postcss@8.5.3):
    postcss-discard-duplicates: private
  /postcss-discard-empty/6.0.3(postcss@8.5.3):
    postcss-discard-empty: private
  /postcss-discard-overridden/6.0.2(postcss@8.5.3):
    postcss-discard-overridden: private
  /postcss-discard-unused/6.0.5(postcss@8.5.3):
    postcss-discard-unused: private
  /postcss-double-position-gradients/6.0.2(postcss@8.5.3):
    postcss-double-position-gradients: private
  /postcss-focus-visible/10.0.1(postcss@8.5.3):
    postcss-focus-visible: private
  /postcss-focus-within/9.0.1(postcss@8.5.3):
    postcss-focus-within: private
  /postcss-font-variant/5.0.0(postcss@8.5.3):
    postcss-font-variant: private
  /postcss-gap-properties/6.0.0(postcss@8.5.3):
    postcss-gap-properties: private
  /postcss-image-set-function/7.0.0(postcss@8.5.3):
    postcss-image-set-function: private
  /postcss-lab-function/7.0.10(postcss@8.5.3):
    postcss-lab-function: private
  /postcss-load-config/4.0.2:
    postcss-load-config: private
  /postcss-loader/7.3.4(postcss@8.5.3)(typescript@5.4.3)(webpack@5.99.9):
    postcss-loader: private
  /postcss-logical/8.1.0(postcss@8.5.3):
    postcss-logical: private
  /postcss-merge-idents/6.0.3(postcss@8.5.3):
    postcss-merge-idents: private
  /postcss-merge-longhand/6.0.5(postcss@8.5.3):
    postcss-merge-longhand: private
  /postcss-merge-rules/6.1.1(postcss@8.5.3):
    postcss-merge-rules: private
  /postcss-minify-font-values/6.1.0(postcss@8.5.3):
    postcss-minify-font-values: private
  /postcss-minify-gradients/6.0.3(postcss@8.5.3):
    postcss-minify-gradients: private
  /postcss-minify-params/6.1.0(postcss@8.5.3):
    postcss-minify-params: private
  /postcss-minify-selectors/6.0.4(postcss@8.5.3):
    postcss-minify-selectors: private
  /postcss-modules-extract-imports/3.1.0(postcss@8.5.3):
    postcss-modules-extract-imports: private
  /postcss-modules-local-by-default/4.2.0(postcss@8.5.3):
    postcss-modules-local-by-default: private
  /postcss-modules-scope/3.2.1(postcss@8.5.3):
    postcss-modules-scope: private
  /postcss-modules-values/4.0.0(postcss@8.5.3):
    postcss-modules-values: private
  /postcss-nesting/13.0.1(postcss@8.5.3):
    postcss-nesting: private
  /postcss-normalize-charset/6.0.2(postcss@8.5.3):
    postcss-normalize-charset: private
  /postcss-normalize-display-values/6.0.2(postcss@8.5.3):
    postcss-normalize-display-values: private
  /postcss-normalize-positions/6.0.2(postcss@8.5.3):
    postcss-normalize-positions: private
  /postcss-normalize-repeat-style/6.0.2(postcss@8.5.3):
    postcss-normalize-repeat-style: private
  /postcss-normalize-string/6.0.2(postcss@8.5.3):
    postcss-normalize-string: private
  /postcss-normalize-timing-functions/6.0.2(postcss@8.5.3):
    postcss-normalize-timing-functions: private
  /postcss-normalize-unicode/6.1.0(postcss@8.5.3):
    postcss-normalize-unicode: private
  /postcss-normalize-url/6.0.2(postcss@8.5.3):
    postcss-normalize-url: private
  /postcss-normalize-whitespace/6.0.2(postcss@8.5.3):
    postcss-normalize-whitespace: private
  /postcss-opacity-percentage/3.0.0(postcss@8.5.3):
    postcss-opacity-percentage: private
  /postcss-ordered-values/6.0.2(postcss@8.5.3):
    postcss-ordered-values: private
  /postcss-overflow-shorthand/6.0.0(postcss@8.5.3):
    postcss-overflow-shorthand: private
  /postcss-page-break/3.0.4(postcss@8.5.3):
    postcss-page-break: private
  /postcss-place/10.0.0(postcss@8.5.3):
    postcss-place: private
  /postcss-preset-env/10.2.0(postcss@8.5.3):
    postcss-preset-env: private
  /postcss-pseudo-class-any-link/10.0.1(postcss@8.5.3):
    postcss-pseudo-class-any-link: private
  /postcss-reduce-idents/6.0.3(postcss@8.5.3):
    postcss-reduce-idents: private
  /postcss-reduce-initial/6.1.0(postcss@8.5.3):
    postcss-reduce-initial: private
  /postcss-reduce-transforms/6.0.2(postcss@8.5.3):
    postcss-reduce-transforms: private
  /postcss-replace-overflow-wrap/4.0.0(postcss@8.5.3):
    postcss-replace-overflow-wrap: private
  /postcss-selector-not/8.0.1(postcss@8.5.3):
    postcss-selector-not: private
  /postcss-selector-parser/7.1.0:
    postcss-selector-parser: private
  /postcss-sort-media-queries/5.2.0(postcss@8.5.3):
    postcss-sort-media-queries: private
  /postcss-svgo/6.0.3(postcss@8.5.3):
    postcss-svgo: private
  /postcss-unique-selectors/6.0.4(postcss@8.5.3):
    postcss-unique-selectors: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /postcss-zindex/6.0.2(postcss@8.5.3):
    postcss-zindex: private
  /postcss/8.5.3:
    postcss: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prettier-linter-helpers/1.0.0:
    prettier-linter-helpers: public
  /pretty-error/4.0.0:
    pretty-error: private
  /pretty-format/29.7.0:
    pretty-format: private
  /pretty-time/1.1.0:
    pretty-time: private
  /prism-react-renderer/2.4.1(react@18.2.0):
    prism-react-renderer: private
  /prismjs/1.30.0:
    prismjs: private
  /process-nextick-args/2.0.1:
    process-nextick-args: private
  /prompts/2.4.2:
    prompts: private
  /prop-types/15.8.1:
    prop-types: private
  /property-information/7.1.0:
    property-information: private
  /proto-list/1.2.4:
    proto-list: private
  /proxy-addr/2.0.7:
    proxy-addr: private
  /psl/1.15.0:
    psl: private
  /pstree.remy/1.1.8:
    pstree.remy: private
  /punycode/2.3.1:
    punycode: private
  /pupa/3.1.0:
    pupa: private
  /pure-rand/6.1.0:
    pure-rand: private
  /qs/6.11.0:
    qs: private
  /querystringify/2.2.0:
    querystringify: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /quick-lru/5.1.1:
    quick-lru: private
  /randombytes/2.1.0:
    randombytes: private
  /range-parser/1.2.1:
    range-parser: private
  /raw-body/2.5.2:
    raw-body: private
  /rc/1.2.8:
    rc: private
  /react-dom/18.2.0(react@18.2.0):
    react-dom: private
  /react-fast-compare/3.2.2:
    react-fast-compare: private
  /react-is/18.3.1:
    react-is: private
  /react-json-view-lite/2.4.1(react@18.2.0):
    react-json-view-lite: private
  /react-loadable-ssr-addon-v5-slorber/1.0.1(@docusaurus/react-loadable@6.0.0)(webpack@5.99.9):
    react-loadable-ssr-addon-v5-slorber: private
  /react-refresh/0.14.2:
    react-refresh: private
  /react-router-config/5.1.1(react-router@5.3.4)(react@18.2.0):
    react-router-config: private
  /react-router-dom/5.3.4(react@18.2.0):
    react-router-dom: private
  /react-router/5.3.4(react@18.2.0):
    react-router: private
  /react/18.2.0:
    react: private
  /readable-stream/3.6.2:
    readable-stream: private
  /readdirp/3.6.0:
    readdirp: private
  /recma-build-jsx/1.0.0:
    recma-build-jsx: private
  /recma-jsx/1.0.0(acorn@8.14.1):
    recma-jsx: private
  /recma-parse/1.0.0:
    recma-parse: private
  /recma-stringify/1.0.0:
    recma-stringify: private
  /redent/3.0.0:
    redent: private
  /regenerate-unicode-properties/10.2.0:
    regenerate-unicode-properties: private
  /regenerate/1.4.2:
    regenerate: private
  /regexp.prototype.flags/1.5.4:
    regexp.prototype.flags: private
  /regexpu-core/6.2.0:
    regexpu-core: private
  /registry-auth-token/5.1.0:
    registry-auth-token: private
  /registry-url/6.0.1:
    registry-url: private
  /regjsgen/0.8.0:
    regjsgen: private
  /regjsparser/0.12.0:
    regjsparser: private
  /rehype-raw/7.0.0:
    rehype-raw: private
  /rehype-recma/1.0.0:
    rehype-recma: private
  /relateurl/0.2.7:
    relateurl: private
  /remark-directive/3.0.1:
    remark-directive: private
  /remark-emoji/4.0.1:
    remark-emoji: private
  /remark-frontmatter/5.0.0:
    remark-frontmatter: private
  /remark-gfm/4.0.1:
    remark-gfm: private
  /remark-mdx/3.1.0:
    remark-mdx: private
  /remark-parse/11.0.0:
    remark-parse: private
  /remark-rehype/11.1.2:
    remark-rehype: private
  /remark-stringify/11.0.0:
    remark-stringify: private
  /renderkid/3.0.0:
    renderkid: private
  /repeat-string/1.6.1:
    repeat-string: private
  /require-directory/2.1.1:
    require-directory: private
  /require-from-string/2.0.2:
    require-from-string: private
  /require-like/0.1.2:
    require-like: private
  /requires-port/1.0.0:
    requires-port: private
  /resolve-alpn/1.2.1:
    resolve-alpn: private
  /resolve-cwd/3.0.0:
    resolve-cwd: private
  /resolve-from/5.0.0:
    resolve-from: private
  /resolve-pathname/3.0.0:
    resolve-pathname: private
  /resolve.exports/2.0.3:
    resolve.exports: private
  /resolve/1.22.10:
    resolve: private
  /responselike/3.0.0:
    responselike: private
  /restore-cursor/5.1.0:
    restore-cursor: private
  /retry/0.13.1:
    retry: private
  /reusify/1.1.0:
    reusify: private
  /rfdc/1.4.1:
    rfdc: private
  /rimraf/3.0.2:
    rimraf: private
  /rollup/4.41.1:
    rollup: private
  /rrweb-cssom/0.7.1:
    rrweb-cssom: private
  /rtlcss/4.3.0:
    rtlcss: private
  /run-parallel/1.2.0:
    run-parallel: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /sax/1.4.1:
    sax: private
  /saxes/6.0.0:
    saxes: private
  /scheduler/0.23.2:
    scheduler: private
  /schema-dts/1.1.5:
    schema-dts: private
  /schema-utils/4.3.2:
    schema-utils: private
  /search-insights/2.17.3:
    search-insights: private
  /section-matter/1.0.0:
    section-matter: private
  /select-hose/2.0.0:
    select-hose: private
  /selfsigned/2.4.1:
    selfsigned: private
  /semver-diff/4.0.0:
    semver-diff: private
  /semver/7.7.2:
    semver: private
  /send/0.18.0:
    send: private
  /serialize-javascript/6.0.2:
    serialize-javascript: private
  /serve-handler/6.1.6:
    serve-handler: private
  /serve-index/1.9.1:
    serve-index: private
  /serve-static/1.15.0:
    serve-static: private
  /set-function-length/1.2.2:
    set-function-length: private
  /set-function-name/2.0.2:
    set-function-name: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /shallow-clone/3.0.1:
    shallow-clone: private
  /shallowequal/1.1.0:
    shallowequal: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /shell-quote/1.8.2:
    shell-quote: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /siginfo/2.0.0:
    siginfo: private
  /signal-exit/3.0.7:
    signal-exit: private
  /simple-update-notifier/2.0.0:
    simple-update-notifier: private
  /sirv/2.0.4:
    sirv: private
  /sisteransi/1.0.5:
    sisteransi: private
  /sitemap/7.1.2:
    sitemap: private
  /skin-tone/2.0.0:
    skin-tone: private
  /slash/3.0.0:
    slash: private
  /slice-ansi/5.0.0:
    slice-ansi: private
  /snake-case/3.0.4:
    snake-case: private
  /sockjs/0.3.24:
    sockjs: private
  /sort-css-media-queries/2.2.0:
    sort-css-media-queries: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-support/0.5.13:
    source-map-support: private
  /source-map/0.8.0-beta.0:
    source-map: private
  /space-separated-tokens/2.0.2:
    space-separated-tokens: private
  /spdy-transport/3.0.0:
    spdy-transport: private
  /spdy/4.0.2:
    spdy: private
  /sprintf-js/1.0.3:
    sprintf-js: private
  /srcset/4.0.0:
    srcset: private
  /stack-utils/2.0.6:
    stack-utils: private
  /stackback/0.0.2:
    stackback: private
  /statuses/2.0.1:
    statuses: private
  /std-env/3.9.0:
    std-env: private
  /stop-iteration-iterator/1.1.0:
    stop-iteration-iterator: private
  /string-argv/0.3.2:
    string-argv: private
  /string-length/4.0.2:
    string-length: private
  /string-width/4.2.3:
    string-width-cjs: private
  /string-width/5.1.2:
    string-width: private
  /string_decoder/1.3.0:
    string_decoder: private
  /stringify-entities/4.0.4:
    stringify-entities: private
  /stringify-object/3.3.0:
    stringify-object: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom-string/1.0.0:
    strip-bom-string: private
  /strip-bom/4.0.0:
    strip-bom: private
  /strip-final-newline/2.0.0:
    strip-final-newline: private
  /strip-indent/3.0.0:
    strip-indent: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /style-to-js/1.1.16:
    style-to-js: private
  /style-to-object/1.0.8:
    style-to-object: private
  /stylehacks/6.1.1(postcss@8.5.3):
    stylehacks: private
  /sucrase/3.35.0:
    sucrase: private
  /superagent/10.2.1:
    superagent: private
  /supports-color/5.5.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /svg-parser/2.0.4:
    svg-parser: private
  /svgo/3.3.2:
    svgo: private
  /symbol-tree/3.2.4:
    symbol-tree: private
  /synckit/0.8.8:
    synckit: private
  /tapable/2.2.2:
    tapable: private
  /terser-webpack-plugin/5.3.14(esbuild@0.19.12)(webpack@5.99.9):
    terser-webpack-plugin: private
  /terser/5.40.0:
    terser: private
  /test-exclude/7.0.1:
    test-exclude: private
  /text-table/0.2.0:
    text-table: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /thunky/1.1.0:
    thunky: private
  /tiny-invariant/1.3.3:
    tiny-invariant: private
  /tiny-warning/1.0.3:
    tiny-warning: private
  /tinybench/2.9.0:
    tinybench: private
  /tinyexec/0.3.2:
    tinyexec: private
  /tinyglobby/0.2.13:
    tinyglobby: private
  /tinypool/1.0.2:
    tinypool: private
  /tinyrainbow/2.0.0:
    tinyrainbow: private
  /tinyspy/3.0.2:
    tinyspy: private
  /tmpl/1.0.5:
    tmpl: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /toidentifier/1.0.1:
    toidentifier: private
  /totalist/3.0.1:
    totalist: private
  /touch/3.1.1:
    touch: private
  /tough-cookie/4.1.4:
    tough-cookie: private
  /tr46/5.1.1:
    tr46: private
  /tree-kill/1.2.2:
    tree-kill: private
  /trim-lines/3.0.1:
    trim-lines: private
  /trough/2.2.0:
    trough: private
  /ts-api-utils/1.4.3(typescript@5.4.3):
    ts-api-utils: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /ts-jest/29.1.2(@babel/core@7.27.1)(esbuild@0.19.12)(jest@29.7.0)(typescript@5.4.3):
    ts-jest: private
  /tslib/2.8.1:
    tslib: private
  /turbo-darwin-64/1.13.2:
    turbo-darwin-64: private
  /turbo-darwin-arm64/1.13.2:
    turbo-darwin-arm64: private
  /turbo-linux-64/1.13.2:
    turbo-linux-64: private
  /turbo-linux-arm64/1.13.2:
    turbo-linux-arm64: private
  /turbo-windows-64/1.13.2:
    turbo-windows-64: private
  /turbo-windows-arm64/1.13.2:
    turbo-windows-arm64: private
  /type-check/0.4.0:
    type-check: private
  /type-detect/4.0.8:
    type-detect: private
  /type-fest/2.19.0:
    type-fest: private
  /type-is/1.6.18:
    type-is: private
  /typedarray-to-buffer/3.1.5:
    typedarray-to-buffer: private
  /undefsafe/2.0.5:
    undefsafe: private
  /undici-types/5.26.5:
    undici-types: private
  /undici/6.21.3:
    undici: private
  /unicode-canonical-property-names-ecmascript/2.0.1:
    unicode-canonical-property-names-ecmascript: private
  /unicode-emoji-modifier-base/1.0.0:
    unicode-emoji-modifier-base: private
  /unicode-match-property-ecmascript/2.0.0:
    unicode-match-property-ecmascript: private
  /unicode-match-property-value-ecmascript/2.2.0:
    unicode-match-property-value-ecmascript: private
  /unicode-property-aliases-ecmascript/2.1.0:
    unicode-property-aliases-ecmascript: private
  /unified/11.0.5:
    unified: private
  /unique-string/3.0.0:
    unique-string: private
  /unist-util-is/6.0.0:
    unist-util-is: private
  /unist-util-position-from-estree/2.0.0:
    unist-util-position-from-estree: private
  /unist-util-position/5.0.0:
    unist-util-position: private
  /unist-util-stringify-position/4.0.0:
    unist-util-stringify-position: private
  /unist-util-visit-parents/6.0.1:
    unist-util-visit-parents: private
  /unist-util-visit/5.0.0:
    unist-util-visit: private
  /universalify/2.0.1:
    universalify: private
  /unpipe/1.0.0:
    unpipe: private
  /update-browserslist-db/1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  /update-notifier/6.0.2:
    update-notifier: private
  /uri-js/4.4.1:
    uri-js: private
  /url-loader/4.1.1(file-loader@6.2.0)(webpack@5.99.9):
    url-loader: private
  /url-parse/1.5.10:
    url-parse: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /utila/0.4.0:
    utila: private
  /utility-types/3.11.0:
    utility-types: private
  /utils-merge/1.0.1:
    utils-merge: private
  /uuid/8.3.2:
    uuid: private
  /v8-to-istanbul/9.3.0:
    v8-to-istanbul: private
  /value-equal/1.0.1:
    value-equal: private
  /vary/1.1.2:
    vary: private
  /vfile-location/5.0.3:
    vfile-location: private
  /vfile-message/4.0.2:
    vfile-message: private
  /vfile/6.0.3:
    vfile: private
  /vite-node/3.1.4(@types/node@20.12.7):
    vite-node: private
  /vite/5.2.2(@types/node@20.12.7):
    vite: private
  /w3c-xmlserializer/5.0.0:
    w3c-xmlserializer: private
  /walker/1.0.8:
    walker: private
  /watchpack/2.4.4:
    watchpack: private
  /wbuf/1.7.3:
    wbuf: private
  /web-namespaces/2.0.1:
    web-namespaces: private
  /webidl-conversions/7.0.0:
    webidl-conversions: private
  /webpack-bundle-analyzer/4.10.2:
    webpack-bundle-analyzer: private
  /webpack-dev-middleware/5.3.4(webpack@5.99.9):
    webpack-dev-middleware: private
  /webpack-dev-server/4.15.2(debug@4.4.1)(webpack@5.99.9):
    webpack-dev-server: private
  /webpack-merge/6.0.1:
    webpack-merge: private
  /webpack-sources/3.3.0:
    webpack-sources: private
  /webpack/5.99.9(esbuild@0.19.12):
    webpack: private
  /webpackbar/6.0.1(webpack@5.99.9):
    webpackbar: private
  /websocket-driver/0.7.4:
    websocket-driver: private
  /websocket-extensions/0.1.4:
    websocket-extensions: private
  /whatwg-encoding/3.1.1:
    whatwg-encoding: private
  /whatwg-mimetype/4.0.0:
    whatwg-mimetype: private
  /whatwg-url/14.2.0:
    whatwg-url: private
  /which-boxed-primitive/1.1.1:
    which-boxed-primitive: private
  /which-collection/1.0.2:
    which-collection: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/2.0.2:
    which: private
  /why-is-node-running/2.3.0:
    why-is-node-running: private
  /widest-line/4.0.1:
    widest-line: private
  /wildcard/2.0.1:
    wildcard: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrap-ansi/8.1.0:
    wrap-ansi: private
  /wrappy/1.0.2:
    wrappy: private
  /write-file-atomic/4.0.2:
    write-file-atomic: private
  /ws/8.18.2:
    ws: private
  /xdg-basedir/5.1.0:
    xdg-basedir: private
  /xml-js/1.6.11:
    xml-js: private
  /xml-name-validator/5.0.0:
    xml-name-validator: private
  /xmlchars/2.2.0:
    xmlchars: private
  /y18n/5.0.8:
    y18n: private
  /yallist/3.1.1:
    yallist: private
  /yaml/2.3.4:
    yaml: private
  /yargs-parser/21.1.1:
    yargs-parser: private
  /yargs/17.7.2:
    yargs: private
  /yocto-queue/0.1.0:
    yocto-queue: private
  /zwitch/2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.4
pendingBuilds: []
prunedAt: Sun, 25 May 2025 12:47:32 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@emnapi/core/1.4.3
  - /@emnapi/runtime/1.4.3
  - /@emnapi/wasi-threads/1.0.2
  - /@esbuild/aix-ppc64/0.19.12
  - /@esbuild/aix-ppc64/0.20.2
  - /@esbuild/android-arm/0.19.12
  - /@esbuild/android-arm/0.20.2
  - /@esbuild/android-arm64/0.19.12
  - /@esbuild/android-arm64/0.20.2
  - /@esbuild/android-x64/0.19.12
  - /@esbuild/android-x64/0.20.2
  - /@esbuild/darwin-x64/0.19.12
  - /@esbuild/darwin-x64/0.20.2
  - /@esbuild/freebsd-arm64/0.19.12
  - /@esbuild/freebsd-arm64/0.20.2
  - /@esbuild/freebsd-x64/0.19.12
  - /@esbuild/freebsd-x64/0.20.2
  - /@esbuild/linux-arm/0.19.12
  - /@esbuild/linux-arm/0.20.2
  - /@esbuild/linux-arm64/0.19.12
  - /@esbuild/linux-arm64/0.20.2
  - /@esbuild/linux-ia32/0.19.12
  - /@esbuild/linux-ia32/0.20.2
  - /@esbuild/linux-loong64/0.19.12
  - /@esbuild/linux-loong64/0.20.2
  - /@esbuild/linux-mips64el/0.19.12
  - /@esbuild/linux-mips64el/0.20.2
  - /@esbuild/linux-ppc64/0.19.12
  - /@esbuild/linux-ppc64/0.20.2
  - /@esbuild/linux-riscv64/0.19.12
  - /@esbuild/linux-riscv64/0.20.2
  - /@esbuild/linux-s390x/0.19.12
  - /@esbuild/linux-s390x/0.20.2
  - /@esbuild/linux-x64/0.19.12
  - /@esbuild/linux-x64/0.20.2
  - /@esbuild/netbsd-x64/0.19.12
  - /@esbuild/netbsd-x64/0.20.2
  - /@esbuild/openbsd-x64/0.19.12
  - /@esbuild/openbsd-x64/0.20.2
  - /@esbuild/sunos-x64/0.19.12
  - /@esbuild/sunos-x64/0.20.2
  - /@esbuild/win32-arm64/0.19.12
  - /@esbuild/win32-arm64/0.20.2
  - /@esbuild/win32-ia32/0.19.12
  - /@esbuild/win32-ia32/0.20.2
  - /@esbuild/win32-x64/0.19.12
  - /@esbuild/win32-x64/0.20.2
  - /@napi-rs/wasm-runtime/0.2.10
  - /@node-rs/jieba-android-arm-eabi/1.10.4
  - /@node-rs/jieba-android-arm64/1.10.4
  - /@node-rs/jieba-darwin-x64/1.10.4
  - /@node-rs/jieba-freebsd-x64/1.10.4
  - /@node-rs/jieba-linux-arm-gnueabihf/1.10.4
  - /@node-rs/jieba-linux-arm64-gnu/1.10.4
  - /@node-rs/jieba-linux-arm64-musl/1.10.4
  - /@node-rs/jieba-linux-x64-gnu/1.10.4
  - /@node-rs/jieba-linux-x64-musl/1.10.4
  - /@node-rs/jieba-wasm32-wasi/1.10.4
  - /@node-rs/jieba-win32-arm64-msvc/1.10.4
  - /@node-rs/jieba-win32-ia32-msvc/1.10.4
  - /@node-rs/jieba-win32-x64-msvc/1.10.4
  - /@rollup/rollup-android-arm-eabi/4.41.1
  - /@rollup/rollup-android-arm64/4.41.1
  - /@rollup/rollup-darwin-x64/4.41.1
  - /@rollup/rollup-freebsd-arm64/4.41.1
  - /@rollup/rollup-freebsd-x64/4.41.1
  - /@rollup/rollup-linux-arm-gnueabihf/4.41.1
  - /@rollup/rollup-linux-arm-musleabihf/4.41.1
  - /@rollup/rollup-linux-arm64-gnu/4.41.1
  - /@rollup/rollup-linux-arm64-musl/4.41.1
  - /@rollup/rollup-linux-loongarch64-gnu/4.41.1
  - /@rollup/rollup-linux-powerpc64le-gnu/4.41.1
  - /@rollup/rollup-linux-riscv64-gnu/4.41.1
  - /@rollup/rollup-linux-riscv64-musl/4.41.1
  - /@rollup/rollup-linux-s390x-gnu/4.41.1
  - /@rollup/rollup-linux-x64-gnu/4.41.1
  - /@rollup/rollup-linux-x64-musl/4.41.1
  - /@rollup/rollup-win32-arm64-msvc/4.41.1
  - /@rollup/rollup-win32-ia32-msvc/4.41.1
  - /@rollup/rollup-win32-x64-msvc/4.41.1
  - /@tybys/wasm-util/0.9.0
  - /turbo-darwin-64/1.13.2
  - /turbo-linux-64/1.13.2
  - /turbo-linux-arm64/1.13.2
  - /turbo-windows-64/1.13.2
  - /turbo-windows-arm64/1.13.2
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
