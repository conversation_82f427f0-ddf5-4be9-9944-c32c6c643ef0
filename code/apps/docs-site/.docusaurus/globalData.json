{"docusaurus-plugin-content-docs": {"default": {"path": "/kloudi-swe-agent/tech-specs", "versions": [{"name": "current", "label": "Next", "isLast": true, "path": "/kloudi-swe-agent/tech-specs", "mainDocId": "structure", "docs": [{"id": "adrs/adr-001-monorepo", "path": "/kloudi-swe-agent/tech-specs/adrs/adr-001-monorepo"}, {"id": "adrs/adr-002-typescript", "path": "/kloudi-swe-agent/tech-specs/adrs/adr-002-typescript"}, {"id": "adrs/adr-003-jsonld", "path": "/kloudi-swe-agent/tech-specs/adrs/adr-003-jsonld"}, {"id": "adrs/adr-007-do<PERSON>aurus", "path": "/kloudi-swe-agent/tech-specs/adrs/adr-007-docusaurus"}, {"id": "adrs/log", "path": "/kloudi-swe-agent/tech-specs/adrs/log"}, {"id": "dependencies", "path": "/kloudi-swe-agent/tech-specs/dependencies"}, {"id": "guides/agent-configuration-guide", "path": "/kloudi-swe-agent/tech-specs/guides/agent-configuration-guide"}, {"id": "milestones/log", "path": "/kloudi-swe-agent/tech-specs/milestones/log"}, {"id": "milestones/milestone-M0", "path": "/kloudi-swe-agent/tech-specs/milestones/milestone-M0"}, {"id": "milestones/milestone-M0.1", "path": "/kloudi-swe-agent/tech-specs/milestones/milestone-M0.1"}, {"id": "milestones/milestone-M0.2", "path": "/kloudi-swe-agent/tech-specs/milestones/milestone-M0.2"}, {"id": "milestones/milestone-M1", "path": "/kloudi-swe-agent/tech-specs/milestones/milestone-M1"}, {"id": "milestones/milestone-TEST", "path": "/kloudi-swe-agent/tech-specs/milestones/milestone-TEST"}, {"id": "milestones/work-log/milestone-m0/conversation-summary", "path": "/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/conversation-summary"}, {"id": "milestones/work-log/milestone-m0/fixes-checklist", "path": "/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/fixes-checklist"}, {"id": "milestones/work-log/milestone-m0/implementation-log", "path": "/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/implementation-log"}, {"id": "milestones/work-log/milestone-m0/technical-reference", "path": "/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/technical-reference"}, {"id": "process/agent-rules/augment", "path": "/kloudi-swe-agent/tech-specs/process/agent-rules/augment"}, {"id": "process/agent-rules/claude", "path": "/kloudi-swe-agent/tech-specs/process/agent-rules/claude"}, {"id": "process/agent-rules/copilot", "path": "/kloudi-swe-agent/tech-specs/process/agent-rules/copilot"}, {"id": "process/agent-rules/core", "path": "/kloudi-swe-agent/tech-specs/process/agent-rules/core"}, {"id": "process/agent-rules/cursor", "path": "/kloudi-swe-agent/tech-specs/process/agent-rules/cursor"}, {"id": "process/agent-rules/custom", "path": "/kloudi-swe-agent/tech-specs/process/agent-rules/custom"}, {"id": "process/agent-rules/validation", "path": "/kloudi-swe-agent/tech-specs/process/agent-rules/validation"}, {"id": "process/core/architectural-decisions", "path": "/kloudi-swe-agent/tech-specs/process/core/architectural-decisions"}, {"id": "process/core/documentation", "path": "/kloudi-swe-agent/tech-specs/process/core/documentation"}, {"id": "process/core/error-recovery", "path": "/kloudi-swe-agent/tech-specs/process/core/error-recovery"}, {"id": "process/core/git-workflow", "path": "/kloudi-swe-agent/tech-specs/process/core/git-workflow"}, {"id": "process/core/milestone-implementation", "path": "/kloudi-swe-agent/tech-specs/process/core/milestone-implementation"}, {"id": "process/core/quality-assurance", "path": "/kloudi-swe-agent/tech-specs/process/core/quality-assurance"}, {"id": "process/migration-guide", "path": "/kloudi-swe-agent/tech-specs/process/migration-guide"}, {"id": "process/optimisation-results/optimisation-result-001-results", "path": "/kloudi-swe-agent/tech-specs/process/optimisation-results/optimisation-result-001-results"}, {"id": "process/README", "path": "/kloudi-swe-agent/tech-specs/process/"}, {"id": "process/templates/adr-template", "path": "/kloudi-swe-agent/tech-specs/process/templates/adr-template"}, {"id": "process/templates/domain-template", "path": "/kloudi-swe-agent/tech-specs/process/templates/domain-template"}, {"id": "process/templates/milestone-template", "path": "/kloudi-swe-agent/tech-specs/process/templates/milestone-template"}, {"id": "process/templates/process-improvement", "path": "/kloudi-swe-agent/tech-specs/process/templates/process-improvement"}, {"id": "process/templates/requirement-checklist", "path": "/kloudi-swe-agent/tech-specs/process/templates/requirement-checklist"}, {"id": "process/templates/work-log-template", "path": "/kloudi-swe-agent/tech-specs/process/templates/work-log-template"}, {"id": "spec_checklist", "path": "/kloudi-swe-agent/tech-specs/spec_checklist"}, {"id": "structure", "path": "/kloudi-swe-agent/tech-specs/structure", "sidebar": "techSpecsSidebar"}], "draftIds": [], "sidebars": {"techSpecsSidebar": {"link": {"path": "/kloudi-swe-agent/tech-specs/structure", "label": "structure"}}}}], "breadcrumbs": true}}}