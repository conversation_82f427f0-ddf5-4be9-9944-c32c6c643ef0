{"tag": {"label": "agent-configuration", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-configuration", "allTagsPath": "/kloudi-swe-agent/tech-specs/tags", "count": 3, "items": [{"id": "process/migration-guide", "title": "Agent Configuration Migration Guide", "description": "Guide for migrating to the streamlined agent configuration system", "permalink": "/kloudi-swe-agent/tech-specs/process/migration-guide"}, {"id": "process/optimisation-results/optimisation-result-001-results", "title": "Agent Configuration Validation Results", "description": "Comprehensive validation results of streamlined agent configuration system", "permalink": "/kloudi-swe-agent/tech-specs/process/optimisation-results/optimisation-result-001-results"}, {"id": "milestones/milestone-TEST", "title": "Milestone TEST - Agent Configuration Validation", "description": "Test milestone to validate streamlined agent configuration system", "permalink": "/kloudi-swe-agent/tech-specs/milestones/milestone-TEST"}], "unlisted": false}}