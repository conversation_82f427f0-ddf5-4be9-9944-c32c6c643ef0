{"tag": {"label": "architecture", "permalink": "/kloudi-swe-agent/tech-specs/tags/architecture", "allTagsPath": "/kloudi-swe-agent/tech-specs/tags", "count": 7, "items": [{"id": "adrs/adr-001-monorepo", "title": "ADR-001 — Monorepo Structure with pnpm Workspaces", "description": "Decision to use pnpm workspaces with apps/ and packages/ structure instead of separate repositories.", "permalink": "/kloudi-swe-agent/tech-specs/adrs/adr-001-monorepo"}, {"id": "adrs/adr-002-typescript", "title": "ADR-002 — TypeScript-First Development", "description": "Decision to use strict TypeScript across frontend, backend, and shared code.", "permalink": "/kloudi-swe-agent/tech-specs/adrs/adr-002-typescript"}, {"id": "adrs/adr-003-jsonld", "title": "ADR-003 — JSON-LD for Graph Representation", "description": "Decision to use JSON-LD as the canonical format for representing workflow graphs.", "permalink": "/kloudi-swe-agent/tech-specs/adrs/adr-003-jsonld"}, {"id": "adrs/adr-007-do<PERSON>aurus", "title": "ADR-007 — Docusaurus for Documentation Site", "description": "Decision to use Docusaurus 2 as the static site generator for rendering technical specifications and project documentation.", "permalink": "/kloudi-swe-agent/tech-specs/adrs/adr-007-docusaurus"}, {"id": "process/templates/adr-template", "title": "ADR-XXX — <Decision Title>", "description": "<Brief description of the architectural decision>", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/adr-template"}, {"id": "process/core/architectural-decisions", "title": "Architectural Decision Process", "description": "Process for creating, reviewing, and managing Architectural Decision Records (ADRs)", "permalink": "/kloudi-swe-agent/tech-specs/process/core/architectural-decisions"}, {"id": "adrs/log", "title": "Architectural Decision Records (ADRs)", "description": "Log of all significant architectural decisions made during development.", "permalink": "/kloudi-swe-agent/tech-specs/adrs/log"}], "unlisted": false}}