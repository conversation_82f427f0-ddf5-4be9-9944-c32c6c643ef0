{"id": "process/core/git-workflow", "title": "Git Workflow Process", "description": "Branching strategies, commit standards, and release processes", "source": "@site/../../../docs/tech-specs/process/core/git-workflow.mdx", "sourceDirName": "process/core", "slug": "/process/core/git-workflow", "permalink": "/kloudi-swe-agent/tech-specs/process/core/git-workflow", "draft": false, "unlisted": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/core/git-workflow.mdx", "tags": [{"inline": true, "label": "process", "permalink": "/kloudi-swe-agent/tech-specs/tags/process"}, {"inline": true, "label": "git", "permalink": "/kloudi-swe-agent/tech-specs/tags/git"}, {"inline": true, "label": "workflow", "permalink": "/kloudi-swe-agent/tech-specs/tags/workflow"}, {"inline": true, "label": "branching", "permalink": "/kloudi-swe-agent/tech-specs/tags/branching"}], "version": "current", "frontMatter": {"title": "Git Workflow Process", "description": "Branching strategies, commit standards, and release processes", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "git", "workflow", "branching"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["development-team"]}}