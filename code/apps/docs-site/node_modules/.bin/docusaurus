#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/@docusaurus+core@2.4.3_@docusaurus+types@2.4.3_esbuild@0.19.12_eslint@8.56.0_react-dom@17.0.2_ibteowplzotf2a6nsroyo3kjbi/node_modules/@docusaurus/core/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/@docusaurus+core@2.4.3_@docusaurus+types@2.4.3_esbuild@0.19.12_eslint@8.56.0_react-dom@17.0.2_ibteowplzotf2a6nsroyo3kjbi/node_modules/@docusaurus/core/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/@docusaurus+core@2.4.3_@docusaurus+types@2.4.3_esbuild@0.19.12_eslint@8.56.0_react-dom@17.0.2_ibteowplzotf2a6nsroyo3kjbi/node_modules/@docusaurus/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/@docusaurus+core@2.4.3_@docusaurus+types@2.4.3_esbuild@0.19.12_eslint@8.56.0_react-dom@17.0.2_ibteowplzotf2a6nsroyo3kjbi/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/@docusaurus+core@2.4.3_@docusaurus+types@2.4.3_esbuild@0.19.12_eslint@8.56.0_react-dom@17.0.2_ibteowplzotf2a6nsroyo3kjbi/node_modules/@docusaurus/core/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/@docusaurus+core@2.4.3_@docusaurus+types@2.4.3_esbuild@0.19.12_eslint@8.56.0_react-dom@17.0.2_ibteowplzotf2a6nsroyo3kjbi/node_modules/@docusaurus/core/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/@docusaurus+core@2.4.3_@docusaurus+types@2.4.3_esbuild@0.19.12_eslint@8.56.0_react-dom@17.0.2_ibteowplzotf2a6nsroyo3kjbi/node_modules/@docusaurus/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/@docusaurus+core@2.4.3_@docusaurus+types@2.4.3_esbuild@0.19.12_eslint@8.56.0_react-dom@17.0.2_ibteowplzotf2a6nsroyo3kjbi/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@docusaurus/core/bin/docusaurus.mjs" "$@"
else
  exec node  "$basedir/../@docusaurus/core/bin/docusaurus.mjs" "$@"
fi
