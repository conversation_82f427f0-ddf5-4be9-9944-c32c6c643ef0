{"name": "docs-site", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "3.8.0", "@docusaurus/preset-classic": "3.8.0", "@easyops-cn/docusaurus-search-local": "^0.44.0", "@mdx-js/react": "^3.1.0", "clsx": "^2.1.1", "prism-react-renderer": "^2.4.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.8.0", "@tsconfig/docusaurus": "^1.0.5", "typescript": "^5.4.3"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.14"}}