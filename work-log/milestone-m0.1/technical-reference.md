---
title: "Technical Reference: Milestone M0.1 - Docusaurus Documentation Site"
milestone: "M0.1"
created: "2025-01-25"
author: "Augment Agent"
---

# Technical Reference: Milestone M0.1

## 🏗️ Architecture Overview

### Directory Structure
```
code/apps/docs-site/
├── docusaurus.config.js     # Main configuration
├── sidebars.js              # Navigation structure
├── src/
│   ├── components/
│   │   └── Callout/         # Custom Callout component
│   │       ├── index.tsx
│   │       └── styles.module.css
│   └── theme/
│       └── MDXComponents.tsx # Global MDX component registration
├── static/                  # Static assets
├── package.json
└── tsconfig.json
```

### Configuration Details

#### Docusaurus Config (`docusaurus.config.js`)
```javascript
{
  title: 'Kloudi SWE Agent Documentation',
  tagline: 'Technical specifications and documentation',
  url: 'https://nitishmehrotra.github.io',
  baseUrl: '/kloudi-swe-agent/',
  organizationName: 'nitishmehrotra',
  projectName: 'kloudi-swe-agent',
  
  presets: [
    ['classic', {
      docs: {
        path: '../../../docs/tech-specs',
        routeBasePath: 'tech-specs',
        sidebarPath: require.resolve('./sidebars.js'),
        editUrl: 'https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/',
      }
    }]
  ]
}
```

#### Sidebar Structure (`sidebars.js`)
```javascript
techSpecsSidebar: [
  'structure',
  'dependencies', 
  'spec_checklist',
  {
    type: 'category',
    label: 'Milestones',
    items: ['milestones/log', 'milestones/milestone-M0', ...]
  },
  {
    type: 'category',
    label: 'Process Documentation',
    items: [
      'process/README',
      { type: 'category', label: 'Core Processes', items: [...] },
      { type: 'category', label: 'Agent Rules', items: [...] },
      { type: 'category', label: 'Templates', items: [...] }
    ]
  },
  // ... ADRs, Guides sections
]
```

## 🔧 Component Implementation

### Callout Component
**Location**: `src/components/Callout/index.tsx`

```typescript
interface CalloutProps {
  emoji?: string;
  children: React.ReactNode;
  type?: 'info' | 'warning' | 'danger' | 'success';
}

export function Callout({ emoji, children, type = 'info' }: CalloutProps) {
  return (
    <div className={`${styles.callout} ${styles[type]}`}>
      {emoji && <span className={styles.emoji}>{emoji}</span>}
      <div className={styles.content}>{children}</div>
    </div>
  );
}
```

**Features**:
- TypeScript support with proper interfaces
- CSS modules for styling
- Dark mode compatibility
- Emoji support
- Multiple type variants (info, warning, danger, success)

### MDX Integration
**Location**: `src/theme/MDXComponents.tsx`

```typescript
import MDXComponents from '@theme-original/MDXComponents';
import { Callout } from '@site/src/components/Callout';

export default {
  ...MDXComponents,
  Callout,
};
```

This makes the Callout component globally available in all MDX files without explicit imports.

## 📦 Dependencies

### Core Dependencies
```json
{
  "@docusaurus/core": "2.4.3",
  "@docusaurus/preset-classic": "2.4.3", 
  "@mdx-js/react": "1.6.22",
  "clsx": "1.2.1",
  "prism-react-renderer": "1.3.5",
  "react": "17.0.2",
  "react-dom": "17.0.2"
}
```

### Dev Dependencies
```json
{
  "@docusaurus/module-type-aliases": "2.4.3",
  "@tsconfig/docusaurus": "1.0.7",
  "typescript": "4.9.5"
}
```

### Search Plugin (Temporarily Disabled)
```json
{
  "@easyops-cn/docusaurus-search-local": "0.10.0"
}
```
**Status**: Disabled due to joi schema version conflicts

## 🚨 Known Issues

### 1. Search Plugin Compatibility
**Issue**: `@easyops-cn/docusaurus-search-local` causes build failures
**Error**: "Cannot mix different versions of joi schemas"
**Root Cause**: Version conflict between plugin dependencies and Docusaurus core
**Workaround**: Plugin temporarily commented out in config

### 2. Build Process Debugging
**Issue**: Build command hanging or failing silently
**Symptoms**: `pnpm build` exits with code 1 but no error output
**Investigation**: Possible MDX parsing issues with existing documentation files

### 3. Version Compatibility
**Issue**: Docusaurus 2.4.3 vs latest 3.8.0
**Impact**: Missing features and potential compatibility issues
**Consideration**: Upgrade to latest version after core functionality works

## 🔄 Workspace Integration

### Root Package.json Scripts
```json
{
  "scripts": {
    "docs:start": "pnpm --filter code/apps/docs-site start",
    "docs:build": "pnpm --filter code/apps/docs-site build", 
    "docs:serve": "pnpm --filter code/apps/docs-site serve"
  }
}
```

### Workspace Configuration
- **Root**: `/Users/<USER>/tmp/kloudi-swe-agent/`
- **Code**: `code/` (main development workspace)
- **Docs Site**: `code/apps/docs-site/`
- **Source Docs**: `docs/tech-specs/`

## 📋 Success Criteria Mapping

| Criterion | Implementation | Status |
|-----------|----------------|--------|
| SC-1: `pnpm run docs:build` exits 0 | Build process configured | ❌ Blocked |
| SC-2: Dev server renders specs | Path configuration complete | ⚠️ Testing |
| SC-3: CI workflow green | Not yet implemented | ⏳ Pending |
| SC-4: README badge | Not yet implemented | ⏳ Pending |
| SC-5: Spec passes lint | Not yet tested | ⏳ Pending |
| SC-6: Acceptance tests pass | Not yet tested | ⏳ Pending |

## 🔍 Debugging Commands

```bash
# Test build process
cd code/apps/docs-site
pnpm build

# Start development server
pnpm start

# Check file paths
ls -la ../../../docs/tech-specs/

# Validate configuration
node -e "console.log(require('./docusaurus.config.js'))"
```

## 📚 References

- [Docusaurus Documentation](https://docusaurus.io/docs)
- [MDX Documentation](https://mdxjs.com/)
- [Docusaurus Search Local Plugin](https://github.com/easyops-cn/docusaurus-search-local)

---

**Last Updated**: 2025-01-25 23:50 by Augment Agent
