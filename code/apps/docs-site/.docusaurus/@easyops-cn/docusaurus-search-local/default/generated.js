import lunr from "/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/lunr@2.3.9/node_modules/lunr/lunr.js";
export const language = ["en"];
export const removeDefaultStopWordFilter = false;
export const removeDefaultStemmer = false;
export { default as Mark } from "/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/mark.js@8.11.1/node_modules/mark.js/dist/mark.js"
export const searchIndexUrl = "search-index{dir}.json?_=b7be6e20";
export const searchResultLimits = 8;
export const searchResultContextMaxLength = 50;
export const explicitSearchResultPath = true;
export const searchBarShortcut = true;
export const searchBarShortcutHint = true;
export const searchBarPosition = "right";
export const docsPluginIdForPreferredVersion = undefined;
export const indexDocs = true;
export const searchContextByPaths = null;
export const hideSearchBarWithNoSearchContext = false;
export const useAllContextsWithNoSearchContext = false;