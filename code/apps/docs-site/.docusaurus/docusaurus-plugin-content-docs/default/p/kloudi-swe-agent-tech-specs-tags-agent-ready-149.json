{"tag": {"label": "agent-ready", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-ready", "allTagsPath": "/kloudi-swe-agent/tech-specs/tags", "count": 3, "items": [{"id": "process/templates/process-improvement", "title": "Process Improvement Template", "description": "Streamlined template for capturing process improvements and lessons learned", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/process-improvement"}, {"id": "process/templates/requirement-checklist", "title": "Requirement Checklist Template", "description": "Streamlined pre-implementation validation checklist", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/requirement-checklist"}, {"id": "process/templates/work-log-template", "title": "Work Log Template", "description": "Streamlined template for milestone implementation work logs", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/work-log-template"}], "unlisted": false}}