{"tag": {"label": "validation", "permalink": "/kloudi-swe-agent/tech-specs/tags/validation", "allTagsPath": "/kloudi-swe-agent/tech-specs/tags", "count": 6, "items": [{"id": "process/agent-rules/validation", "title": "Agent Configuration Validation", "description": "Tools and procedures for validating AI agent configurations", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/validation"}, {"id": "process/optimisation-results/optimisation-result-001-results", "title": "Agent Configuration Validation Results", "description": "Comprehensive validation results of streamlined agent configuration system", "permalink": "/kloudi-swe-agent/tech-specs/process/optimisation-results/optimisation-result-001-results"}, {"id": "process/core/documentation", "title": "Documentation Process", "description": "Documentation standards, validation requirements, and maintenance procedures", "permalink": "/kloudi-swe-agent/tech-specs/process/core/documentation"}, {"id": "milestones/milestone-TEST", "title": "Milestone TEST - Agent Configuration Validation", "description": "Test milestone to validate streamlined agent configuration system", "permalink": "/kloudi-swe-agent/tech-specs/milestones/milestone-TEST"}, {"id": "process/core/quality-assurance", "title": "Quality Assurance Process", "description": "Validation, testing, and review processes for ensuring high-quality deliverables", "permalink": "/kloudi-swe-agent/tech-specs/process/core/quality-assurance"}, {"id": "spec_checklist", "title": "Spec Checklist", "description": "Quick reference for milestone specification validation requirements", "permalink": "/kloudi-swe-agent/tech-specs/spec_checklist"}], "unlisted": false}}