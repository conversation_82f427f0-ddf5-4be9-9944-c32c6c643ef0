{"archive": {"blogPosts": [{"id": "welcome", "metadata": {"permalink": "/kloudi-swe-agent/blog/welcome", "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/blog/blog/2021-08-26-welcome/index.md", "source": "@site/blog/2021-08-26-welcome/index.md", "title": "Welcome", "description": "Docusaurus blogging features are powered by the blog plugin.", "date": "2021-08-26T00:00:00.000Z", "tags": [{"inline": true, "label": "facebook", "permalink": "/kloudi-swe-agent/blog/tags/facebook"}, {"inline": true, "label": "hello", "permalink": "/kloudi-swe-agent/blog/tags/hello"}, {"inline": true, "label": "<PERSON>cusaurus", "permalink": "/kloudi-swe-agent/blog/tags/docusaurus"}], "readingTime": 0.52, "hasTruncateMarker": false, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "imageURL": "https://github.com/slorber.png", "key": "slorber", "page": null}, {"name": "<PERSON><PERSON><PERSON>", "title": "Front End Engineer @ Facebook", "url": "https://github.com/yangshun", "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n", "page": null}], "frontMatter": {"slug": "welcome", "title": "Welcome", "authors": ["slorber", "yang<PERSON>n"], "tags": ["facebook", "hello", "<PERSON>cusaurus"]}, "unlisted": false, "nextItem": {"title": "MDX Blog Post", "permalink": "/kloudi-swe-agent/blog/mdx-blog-post"}}, "content": "[Docusaurus blogging features](https://docusaurus.io/docs/blog) are powered by the [blog plugin](https://docusaurus.io/docs/api/plugins/@docusaurus/plugin-content-blog).\n\nSimply add Markdown files (or folders) to the `blog` directory.\n\nRegular blog authors can be added to `authors.yml`.\n\nThe blog post date can be extracted from filenames, such as:\n\n- `2019-05-30-welcome.md`\n- `2019-05-30-welcome/index.md`\n\nA blog post folder can be convenient to co-locate blog post images:\n\n![Docusaurus Plushie](./docusaurus-plushie-banner.jpeg)\n\nThe blog supports tags as well!\n\n**And if you don't want a blog**: just delete this directory, and use `blog: false` in your Docusaurus config."}, {"id": "mdx-blog-post", "metadata": {"permalink": "/kloudi-swe-agent/blog/mdx-blog-post", "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/blog/blog/2021-08-01-mdx-blog-post.mdx", "source": "@site/blog/2021-08-01-mdx-blog-post.mdx", "title": "MDX Blog Post", "description": "Blog posts support Docusaurus Markdown features, such as MDX.", "date": "2021-08-01T00:00:00.000Z", "tags": [{"inline": true, "label": "<PERSON>cusaurus", "permalink": "/kloudi-swe-agent/blog/tags/docusaurus"}], "readingTime": 0.22, "hasTruncateMarker": false, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "imageURL": "https://github.com/slorber.png", "key": "slorber", "page": null}], "frontMatter": {"slug": "mdx-blog-post", "title": "MDX Blog Post", "authors": ["slorber"], "tags": ["<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Welcome", "permalink": "/kloudi-swe-agent/blog/welcome"}, "nextItem": {"title": "Long Blog Post", "permalink": "/kloudi-swe-agent/blog/long-blog-post"}}, "content": "Blog posts support [Docusaurus Markdown features](https://docusaurus.io/docs/markdown-features), such as [MDX](https://mdxjs.com/).\n\n:::tip\n\nUse the power of React to create interactive blog posts.\n\n```js\n<button onClick={() => alert('button clicked!')}>Click me!</button>\n```\n\n<button onClick={() => alert('button clicked!')}>Click me!</button>\n\n:::"}, {"id": "long-blog-post", "metadata": {"permalink": "/kloudi-swe-agent/blog/long-blog-post", "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/blog/blog/2019-05-29-long-blog-post.md", "source": "@site/blog/2019-05-29-long-blog-post.md", "title": "Long Blog Post", "description": "This is the summary of a very long blog post,", "date": "2019-05-29T00:00:00.000Z", "tags": [{"inline": true, "label": "hello", "permalink": "/kloudi-swe-agent/blog/tags/hello"}, {"inline": true, "label": "<PERSON>cusaurus", "permalink": "/kloudi-swe-agent/blog/tags/docusaurus"}], "readingTime": 2.04, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON>", "title": "Maintainer of Docusaurus", "url": "https://github.com/endiliey", "imageURL": "https://github.com/endiliey.png", "key": "endi", "page": null}], "frontMatter": {"slug": "long-blog-post", "title": "Long Blog Post", "authors": "endi", "tags": ["hello", "<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "MDX Blog Post", "permalink": "/kloudi-swe-agent/blog/mdx-blog-post"}, "nextItem": {"title": "First Blog Post", "permalink": "/kloudi-swe-agent/blog/first-blog-post"}}, "content": "This is the summary of a very long blog post,\n\nUse a `<!--` `truncate` `-->` comment to limit blog post size in the list view.\n\n<!--truncate-->\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet"}, {"id": "first-blog-post", "metadata": {"permalink": "/kloudi-swe-agent/blog/first-blog-post", "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/blog/blog/2019-05-28-first-blog-post.md", "source": "@site/blog/2019-05-28-first-blog-post.md", "title": "First Blog Post", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet", "date": "2019-05-28T00:00:00.000Z", "tags": [{"inline": true, "label": "hola", "permalink": "/kloudi-swe-agent/blog/tags/hola"}, {"inline": true, "label": "<PERSON>cusaurus", "permalink": "/kloudi-swe-agent/blog/tags/docusaurus"}], "readingTime": 0.12, "hasTruncateMarker": false, "authors": [{"name": "<PERSON>", "title": "Docusaurus Core Team", "url": "https://github.com/wgao19", "image_url": "https://github.com/wgao19.png", "imageURL": "https://github.com/wgao19.png", "socials": {}, "key": null, "page": null}], "frontMatter": {"slug": "first-blog-post", "title": "First Blog Post", "authors": {"name": "<PERSON>", "title": "Docusaurus Core Team", "url": "https://github.com/wgao19", "image_url": "https://github.com/wgao19.png", "imageURL": "https://github.com/wgao19.png"}, "tags": ["hola", "<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Long Blog Post", "permalink": "/kloudi-swe-agent/blog/long-blog-post"}}, "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet"}]}}