{"unversionedId": "adrs/adr-002-typescript", "id": "adrs/adr-002-typescript", "title": "ADR-002 — TypeScript-First Development", "description": "Decision to use strict TypeScript across frontend, backend, and shared code.", "source": "@site/../../../docs/tech-specs/adrs/adr-002-typescript.mdx", "sourceDirName": "adrs", "slug": "/adrs/adr-002-typescript", "permalink": "/kloudi-swe-agent/tech-specs/adrs/adr-002-typescript", "draft": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/adrs/adr-002-typescript.mdx", "tags": [{"label": "adr", "permalink": "/kloudi-swe-agent/tech-specs/tags/adr"}, {"label": "architecture", "permalink": "/kloudi-swe-agent/tech-specs/tags/architecture"}, {"label": "typescript", "permalink": "/kloudi-swe-agent/tech-specs/tags/typescript"}], "version": "current", "frontMatter": {"title": "ADR-002 — TypeScript-First Development", "description": "Decision to use strict TypeScript across frontend, backend, and shared code.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "typescript"], "authors": ["nitishMeh<PERSON><PERSON>"]}}