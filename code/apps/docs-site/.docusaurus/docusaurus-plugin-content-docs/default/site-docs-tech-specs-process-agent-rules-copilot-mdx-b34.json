{"unversionedId": "process/agent-rules/copilot", "id": "process/agent-rules/copilot", "title": "GitHub Copilot Agent Configuration", "description": "GitHub Copilot specific configuration for milestone implementation", "source": "@site/../../../docs/tech-specs/process/agent-rules/copilot.mdx", "sourceDirName": "process/agent-rules", "slug": "/process/agent-rules/copilot", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/copilot", "draft": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/agent-rules/copilot.mdx", "tags": [{"label": "agent-rules", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-rules"}, {"label": "copilot", "permalink": "/kloudi-swe-agent/tech-specs/tags/copilot"}, {"label": "github", "permalink": "/kloudi-swe-agent/tech-specs/tags/github"}, {"label": "executable", "permalink": "/kloudi-swe-agent/tech-specs/tags/executable"}], "version": "current", "frontMatter": {"title": "GitHub Copilot Agent Configuration", "description": "GitHub Copilot specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "copilot", "github", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}}