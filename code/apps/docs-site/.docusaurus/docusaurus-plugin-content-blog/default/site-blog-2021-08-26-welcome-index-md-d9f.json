{"permalink": "/kloudi-swe-agent/blog/welcome", "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/blog/blog/2021-08-26-welcome/index.md", "source": "@site/blog/2021-08-26-welcome/index.md", "title": "Welcome", "description": "Docusaurus blogging features are powered by the blog plugin.", "date": "2021-08-26T00:00:00.000Z", "tags": [{"inline": true, "label": "facebook", "permalink": "/kloudi-swe-agent/blog/tags/facebook"}, {"inline": true, "label": "hello", "permalink": "/kloudi-swe-agent/blog/tags/hello"}, {"inline": true, "label": "<PERSON>cusaurus", "permalink": "/kloudi-swe-agent/blog/tags/docusaurus"}], "readingTime": 0.52, "hasTruncateMarker": false, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "imageURL": "https://github.com/slorber.png", "key": "slorber", "page": null}, {"name": "<PERSON><PERSON><PERSON>", "title": "Front End Engineer @ Facebook", "url": "https://github.com/yangshun", "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n", "page": null}], "frontMatter": {"slug": "welcome", "title": "Welcome", "authors": ["slorber", "yang<PERSON>n"], "tags": ["facebook", "hello", "<PERSON>cusaurus"]}, "unlisted": false, "nextItem": {"title": "MDX Blog Post", "permalink": "/kloudi-swe-agent/blog/mdx-blog-post"}}