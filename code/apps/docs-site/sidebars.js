/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */

// @ts-check

/** @type {import('@docusaurus/plugin-content-docs').SidebarsConfig} */
const sidebars = {
  // Tech Specs sidebar with organized structure
  techSpecsSidebar: [
    'structure',
    'dependencies',
    'spec_checklist',
    {
      type: 'category',
      label: 'Milestones',
      items: [
        'milestones/log',
        'milestones/milestone-M0',
        'milestones/milestone-M0.1',
        'milestones/milestone-M0.2',
        'milestones/milestone-M1',
        'milestones/milestone-TEST',
      ],
    },
    {
      type: 'category',
      label: 'Process Documentation',
      items: [
        'process/README',
        'process/migration-guide',
        {
          type: 'category',
          label: 'Core Processes',
          items: [
            'process/core/milestone-implementation',
            'process/core/quality-assurance',
            'process/core/git-workflow',
            'process/core/documentation',
            'process/core/architectural-decisions',
            'process/core/error-recovery',
          ],
        },
        {
          type: 'category',
          label: 'Agent Rules',
          items: [
            'process/agent-rules/core',
            'process/agent-rules/augment',
            'process/agent-rules/cursor',
            'process/agent-rules/claude',
            'process/agent-rules/copilot',
            'process/agent-rules/custom',
            'process/agent-rules/validation',
          ],
        },
        {
          type: 'category',
          label: 'Templates',
          items: [
            'process/templates/milestone-template',
            'process/templates/adr-template',
            'process/templates/domain-template',
            'process/templates/work-log-template',
            'process/templates/requirement-checklist',
            'process/templates/process-improvement',
          ],
        },
        {
          type: 'category',
          label: 'Optimization Results',
          items: [
            'process/optimisation-results/optimisation-result-001-results',
          ],
        },
      ],
    },
    {
      type: 'category',
      label: 'Architecture Decision Records',
      items: [
        'adrs/log',
        'adrs/adr-001-monorepo',
        'adrs/adr-002-typescript',
        'adrs/adr-003-jsonld',
        'adrs/adr-007-docusaurus',
      ],
    },
    {
      type: 'category',
      label: 'Guides',
      items: ['guides/agent-configuration-guide'],
    },
    // Domains commented out until content is available
    // {
    //   type: 'category',
    //   label: 'Domains',
    //   items: [{ type: 'autogenerated', dirName: 'domains' }],
    // },
  ],
};

module.exports = sidebars;
