{"tag": {"label": "milestone", "permalink": "/kloudi-swe-agent/tech-specs/tags/milestone", "allTagsPath": "/kloudi-swe-agent/tech-specs/tags", "count": 7, "items": [{"id": "process/templates/milestone-template", "title": "Milestone <ID> — <One-line scope>", "description": "<Short paragraph of intent>", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/milestone-template"}, {"id": "process/core/milestone-implementation", "title": "Milestone Implementation Process", "description": "Comprehensive process for executing milestone specifications", "permalink": "/kloudi-swe-agent/tech-specs/process/core/milestone-implementation"}, {"id": "milestones/milestone-M0", "title": "Milestone M0 — Repository Skeleton & CI", "description": "The contractual scope, decisions, and acceptance tests for the very first deliverable.", "permalink": "/kloudi-swe-agent/tech-specs/milestones/milestone-M0"}, {"id": "milestones/milestone-M0.1", "title": "Milestone M0.1 — Docusaurus Documentation Site", "description": "Bootstrap a static documentation site (Docusaurus 2) that renders all MDX technical specifications.", "permalink": "/kloudi-swe-agent/tech-specs/milestones/milestone-M0.1"}, {"id": "milestones/milestone-M0.2", "title": "Milestone M0.2 — Remove Agent Dry-Run References", "description": "Clean up all traces of agent dry-run functionality from the repository, including package.json scripts, CI workflows, documentation, and work logs.", "permalink": "/kloudi-swe-agent/tech-specs/milestones/milestone-M0.2"}, {"id": "milestones/milestone-M1", "title": "Milestone M1 — Static Graph Builder", "description": "Implements static code analysis and JSON-LD graph extraction for Python/JS using Tree-sitter.", "permalink": "/kloudi-swe-agent/tech-specs/milestones/milestone-M1"}, {"id": "process/templates/work-log-template", "title": "Work Log Template", "description": "Streamlined template for milestone implementation work logs", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/work-log-template"}], "unlisted": false}}