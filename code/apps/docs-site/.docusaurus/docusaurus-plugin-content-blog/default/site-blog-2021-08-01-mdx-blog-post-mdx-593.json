{"permalink": "/kloudi-swe-agent/blog/mdx-blog-post", "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/blog/blog/2021-08-01-mdx-blog-post.mdx", "source": "@site/blog/2021-08-01-mdx-blog-post.mdx", "title": "MDX Blog Post", "description": "Blog posts support Docusaurus Markdown features, such as MDX.", "date": "2021-08-01T00:00:00.000Z", "tags": [{"inline": true, "label": "<PERSON>cusaurus", "permalink": "/kloudi-swe-agent/blog/tags/docusaurus"}], "readingTime": 0.22, "hasTruncateMarker": false, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "imageURL": "https://github.com/slorber.png", "key": "slorber", "page": null}], "frontMatter": {"slug": "mdx-blog-post", "title": "MDX Blog Post", "authors": ["slorber"], "tags": ["<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Welcome", "permalink": "/kloudi-swe-agent/blog/welcome"}, "nextItem": {"title": "Long Blog Post", "permalink": "/kloudi-swe-agent/blog/long-blog-post"}}