{"pluginId": "default", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"techSpecsSidebar": [{"type": "link", "label": "Repository Structure & Conventions", "href": "/kloudi-swe-agent/tech-specs/structure", "docId": "structure"}]}, "docs": {"adrs/adr-001-monorepo": {"id": "adrs/adr-001-monorepo", "title": "ADR-001 — Monorepo Structure with pnpm Workspaces", "description": "Decision to use pnpm workspaces with apps/ and packages/ structure instead of separate repositories."}, "adrs/adr-002-typescript": {"id": "adrs/adr-002-typescript", "title": "ADR-002 — TypeScript-First Development", "description": "Decision to use strict TypeScript across frontend, backend, and shared code."}, "adrs/adr-003-jsonld": {"id": "adrs/adr-003-jsonld", "title": "ADR-003 — JSON-LD for Graph Representation", "description": "Decision to use JSON-LD as the canonical format for representing workflow graphs."}, "adrs/adr-007-docusaurus": {"id": "adrs/adr-007-do<PERSON>aurus", "title": "ADR-007 — Docusaurus for Documentation Site", "description": "Decision to use Docusaurus 2 as the static site generator for rendering technical specifications and project documentation."}, "adrs/log": {"id": "adrs/log", "title": "Architectural Decision Records (ADRs)", "description": "Log of all significant architectural decisions made during development."}, "dependencies": {"id": "dependencies", "title": "Dependencies Documentation", "description": "📋 Purpose: Track all project dependencies, their purposes, and maintenance status. Referenced from docs/tech-specs/00_structure.mdx."}, "guides/agent-configuration-guide": {"id": "guides/agent-configuration-guide", "title": "Agent Configuration Guide", "description": "How to configure AI agents with milestone process requirements"}, "milestones/log": {"id": "milestones/log", "title": "Milestone Progress Log", "description": "Index and progress tracking for all project milestones."}, "milestones/milestone-M0": {"id": "milestones/milestone-M0", "title": "Milestone M0 — Repository Skeleton & CI", "description": "The contractual scope, decisions, and acceptance tests for the very first deliverable."}, "milestones/milestone-M0.1": {"id": "milestones/milestone-M0.1", "title": "Milestone M0.1 — Docusaurus Documentation Site", "description": "Bootstrap a static documentation site (Docusaurus 2) that renders all MDX technical specifications."}, "milestones/milestone-M0.2": {"id": "milestones/milestone-M0.2", "title": "Milestone M0.2 — Remove Agent Dry-Run References", "description": "Clean up all traces of agent dry-run functionality from the repository, including package.json scripts, CI workflows, documentation, and work logs."}, "milestones/milestone-M1": {"id": "milestones/milestone-M1", "title": "Milestone M1 — Static Graph Builder", "description": "Implements static code analysis and JSON-LD graph extraction for Python/JS using Tree-sitter."}, "milestones/milestone-TEST": {"id": "milestones/milestone-TEST", "title": "Milestone TEST - Agent Configuration Validation", "description": "Test milestone to validate streamlined agent configuration system"}, "milestones/work-log/milestone-m0/conversation-summary": {"id": "milestones/work-log/milestone-m0/conversation-summary", "title": "Milestone M0 Conversation Summary", "description": "Date: 2025-05-25"}, "milestones/work-log/milestone-m0/fixes-checklist": {"id": "milestones/work-log/milestone-m0/fixes-checklist", "title": "M0 Immediate Fixes Checklist", "description": "Date: 2025-05-25"}, "milestones/work-log/milestone-m0/implementation-log": {"id": "milestones/work-log/milestone-m0/implementation-log", "title": "Milestone M0 Implementation Work Log", "description": "Date: 2025-05-25"}, "milestones/work-log/milestone-m0/technical-reference": {"id": "milestones/work-log/milestone-m0/technical-reference", "title": "Milestone M0 Technical Reference", "description": "Quick reference for the technical implementation details of M0"}, "process/agent-rules/augment": {"id": "process/agent-rules/augment", "title": "Augment Agent Configuration", "description": "Augment Agent specific configuration for milestone implementation"}, "process/agent-rules/claude": {"id": "process/agent-rules/claude", "title": "Claude/Anthropic Agent Configuration", "description": "Claude/Anthropic specific configuration for milestone implementation"}, "process/agent-rules/copilot": {"id": "process/agent-rules/copilot", "title": "GitHub Copilot Agent Configuration", "description": "GitHub Copilot specific configuration for milestone implementation"}, "process/agent-rules/core": {"id": "process/agent-rules/core", "title": "Core Agent Rules", "description": "Universal executable rules for all AI software engineering agents"}, "process/agent-rules/cursor": {"id": "process/agent-rules/cursor", "title": "Cursor Agent Configuration", "description": "Cursor AI specific configuration for milestone implementation"}, "process/agent-rules/custom": {"id": "process/agent-rules/custom", "title": "Custom Agent Configuration Template", "description": "Template for configuring custom AI agents for milestone implementation"}, "process/agent-rules/validation": {"id": "process/agent-rules/validation", "title": "Agent Configuration Validation", "description": "Tools and procedures for validating AI agent configurations"}, "process/core/architectural-decisions": {"id": "process/core/architectural-decisions", "title": "Architectural Decision Process", "description": "Process for creating, reviewing, and managing Architectural Decision Records (ADRs)"}, "process/core/documentation": {"id": "process/core/documentation", "title": "Documentation Process", "description": "Documentation standards, validation requirements, and maintenance procedures"}, "process/core/error-recovery": {"id": "process/core/error-recovery", "title": "Error Recovery & Rollback Process", "description": "Procedures for handling implementation failures, errors, and rollback scenarios"}, "process/core/git-workflow": {"id": "process/core/git-workflow", "title": "Git Workflow Process", "description": "Branching strategies, commit standards, and release processes"}, "process/core/milestone-implementation": {"id": "process/core/milestone-implementation", "title": "Milestone Implementation Process", "description": "Comprehensive process for executing milestone specifications"}, "process/core/quality-assurance": {"id": "process/core/quality-assurance", "title": "Quality Assurance Process", "description": "Validation, testing, and review processes for ensuring high-quality deliverables"}, "process/migration-guide": {"id": "process/migration-guide", "title": "Agent Configuration Migration Guide", "description": "Guide for migrating to the streamlined agent configuration system"}, "process/optimisation-results/optimisation-result-001-results": {"id": "process/optimisation-results/optimisation-result-001-results", "title": "Agent Configuration Validation Results", "description": "Comprehensive validation results of streamlined agent configuration system"}, "process/README": {"id": "process/README", "title": "Process Documentation Hub", "description": "Central navigation for all development processes and agent configurations"}, "process/templates/adr-template": {"id": "process/templates/adr-template", "title": "ADR-XXX — <Decision Title>", "description": "<Brief description of the architectural decision>"}, "process/templates/domain-template": {"id": "process/templates/domain-template", "title": "Domain Spec — <Domain Name>", "description": "<Brief description of the domain and its scope>"}, "process/templates/milestone-template": {"id": "process/templates/milestone-template", "title": "Milestone <ID> — <One-line scope>", "description": "<Short paragraph of intent>"}, "process/templates/process-improvement": {"id": "process/templates/process-improvement", "title": "Process Improvement Template", "description": "Streamlined template for capturing process improvements and lessons learned"}, "process/templates/requirement-checklist": {"id": "process/templates/requirement-checklist", "title": "Requirement Checklist Template", "description": "Streamlined pre-implementation validation checklist"}, "process/templates/work-log-template": {"id": "process/templates/work-log-template", "title": "Work Log Template", "description": "Streamlined template for milestone implementation work logs"}, "spec_checklist": {"id": "spec_checklist", "title": "Spec Checklist", "description": "Quick reference for milestone specification validation requirements"}, "structure": {"id": "structure", "title": "Repository Structure & Conventions", "description": "Living guideline—update with every structural PR. Single source of truth for project structure.", "sidebar": "techSpecsSidebar"}}}