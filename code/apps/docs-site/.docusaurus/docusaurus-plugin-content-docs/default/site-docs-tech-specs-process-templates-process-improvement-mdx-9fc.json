{"unversionedId": "process/templates/process-improvement", "id": "process/templates/process-improvement", "title": "Process Improvement Template", "description": "Streamlined template for capturing process improvements and lessons learned", "source": "@site/../../../docs/tech-specs/process/templates/process-improvement.mdx", "sourceDirName": "process/templates", "slug": "/process/templates/process-improvement", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/process-improvement", "draft": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/templates/process-improvement.mdx", "tags": [{"label": "template", "permalink": "/kloudi-swe-agent/tech-specs/tags/template"}, {"label": "process-improvement", "permalink": "/kloudi-swe-agent/tech-specs/tags/process-improvement"}, {"label": "lessons-learned", "permalink": "/kloudi-swe-agent/tech-specs/tags/lessons-learned"}, {"label": "agent-ready", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-ready"}], "version": "current", "frontMatter": {"title": "Process Improvement Template", "description": "Streamlined template for capturing process improvements and lessons learned", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["template", "process-improvement", "lessons-learned", "agent-ready"], "authors": ["nitishMeh<PERSON><PERSON>"]}}