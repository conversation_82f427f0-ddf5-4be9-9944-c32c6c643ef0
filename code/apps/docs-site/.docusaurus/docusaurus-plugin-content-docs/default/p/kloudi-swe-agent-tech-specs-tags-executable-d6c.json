{"tag": {"label": "executable", "permalink": "/kloudi-swe-agent/tech-specs/tags/executable", "allTagsPath": "/kloudi-swe-agent/tech-specs/tags", "count": 6, "items": [{"id": "process/agent-rules/augment", "title": "Augment Agent Configuration", "description": "Augment Agent specific configuration for milestone implementation", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/augment"}, {"id": "process/agent-rules/claude", "title": "Claude/Anthropic Agent Configuration", "description": "Claude/Anthropic specific configuration for milestone implementation", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/claude"}, {"id": "process/agent-rules/core", "title": "Core Agent Rules", "description": "Universal executable rules for all AI software engineering agents", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/core"}, {"id": "process/agent-rules/cursor", "title": "Cursor Agent Configuration", "description": "Cursor AI specific configuration for milestone implementation", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/cursor"}, {"id": "process/agent-rules/custom", "title": "Custom Agent Configuration Template", "description": "Template for configuring custom AI agents for milestone implementation", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/custom"}, {"id": "process/agent-rules/copilot", "title": "GitHub Copilot Agent Configuration", "description": "GitHub Copilot specific configuration for milestone implementation", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/copilot"}], "unlisted": false}}