{"unversionedId": "process/agent-rules/claude", "id": "process/agent-rules/claude", "title": "Claude/Anthropic Agent Configuration", "description": "Claude/Anthropic specific configuration for milestone implementation", "source": "@site/../../../docs/tech-specs/process/agent-rules/claude.mdx", "sourceDirName": "process/agent-rules", "slug": "/process/agent-rules/claude", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/claude", "draft": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/agent-rules/claude.mdx", "tags": [{"label": "agent-rules", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-rules"}, {"label": "claude", "permalink": "/kloudi-swe-agent/tech-specs/tags/claude"}, {"label": "anthropic", "permalink": "/kloudi-swe-agent/tech-specs/tags/anthropic"}, {"label": "executable", "permalink": "/kloudi-swe-agent/tech-specs/tags/executable"}], "version": "current", "frontMatter": {"title": "Claude/Anthropic Agent Configuration", "description": "Claude/Anthropic specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "claude", "anthropic", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}}