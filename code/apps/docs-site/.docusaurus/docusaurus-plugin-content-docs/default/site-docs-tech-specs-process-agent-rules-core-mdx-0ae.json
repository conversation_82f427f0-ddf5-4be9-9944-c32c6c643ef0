{"unversionedId": "process/agent-rules/core", "id": "process/agent-rules/core", "title": "Core Agent Rules", "description": "Universal executable rules for all AI software engineering agents", "source": "@site/../../../docs/tech-specs/process/agent-rules/core.mdx", "sourceDirName": "process/agent-rules", "slug": "/process/agent-rules/core", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/core", "draft": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/agent-rules/core.mdx", "tags": [{"label": "agent-rules", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-rules"}, {"label": "core", "permalink": "/kloudi-swe-agent/tech-specs/tags/core"}, {"label": "executable", "permalink": "/kloudi-swe-agent/tech-specs/tags/executable"}], "version": "current", "frontMatter": {"title": "Core Agent Rules", "description": "Universal executable rules for all AI software engineering agents", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "core", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}}