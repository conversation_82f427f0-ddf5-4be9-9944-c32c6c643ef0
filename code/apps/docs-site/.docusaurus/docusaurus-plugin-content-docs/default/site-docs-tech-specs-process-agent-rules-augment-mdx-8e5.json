{"id": "process/agent-rules/augment", "title": "Augment Agent Configuration", "description": "Augment Agent specific configuration for milestone implementation", "source": "@site/../../../docs/tech-specs/process/agent-rules/augment.mdx", "sourceDirName": "process/agent-rules", "slug": "/process/agent-rules/augment", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/augment", "draft": false, "unlisted": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/agent-rules/augment.mdx", "tags": [{"inline": true, "label": "agent-rules", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-rules"}, {"inline": true, "label": "augment", "permalink": "/kloudi-swe-agent/tech-specs/tags/augment"}, {"inline": true, "label": "executable", "permalink": "/kloudi-swe-agent/tech-specs/tags/executable"}], "version": "current", "frontMatter": {"title": "Augment Agent Configuration", "description": "Augment Agent specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "augment", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}}