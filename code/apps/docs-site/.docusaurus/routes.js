import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/kloudi-swe-agent/blog',
    component: ComponentCreator('/kloudi-swe-agent/blog', '46a'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/archive',
    component: ComponentCreator('/kloudi-swe-agent/blog/archive', '390'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/first-blog-post',
    component: ComponentCreator('/kloudi-swe-agent/blog/first-blog-post', '3fc'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/long-blog-post',
    component: ComponentCreator('/kloudi-swe-agent/blog/long-blog-post', '15d'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/mdx-blog-post',
    component: ComponentCreator('/kloudi-swe-agent/blog/mdx-blog-post', 'ce6'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags', 'fca'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags/docusaurus',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags/docusaurus', '6ec'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags/facebook',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags/facebook', '1d0'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags/hello',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags/hello', '45a'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/tags/hola',
    component: ComponentCreator('/kloudi-swe-agent/blog/tags/hola', 'e3f'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/blog/welcome',
    component: ComponentCreator('/kloudi-swe-agent/blog/welcome', '7b3'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/markdown-page',
    component: ComponentCreator('/kloudi-swe-agent/markdown-page', '9e9'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags', 'cc8'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/adr',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/adr', '864'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/agent-configuration',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/agent-configuration', 'c00'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/agent-ready',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/agent-ready', '2b8'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/agent-rules',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/agent-rules', 'ff5'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/agents',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/agents', 'fa0'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/anthropic',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/anthropic', '484'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/architecture',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/architecture', '733'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/augment',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/augment', '9d1'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/branching',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/branching', 'e7c'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/checklist',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/checklist', 'f90'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/claude',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/claude', '16d'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/cleanup',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/cleanup', 'ac4'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/configuration',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/configuration', '68a'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/copilot',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/copilot', '692'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/core',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/core', '075'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/cursor',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/cursor', '68d'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/custom',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/custom', 'fc7'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/data-format',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/data-format', '877'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/decisions',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/decisions', '972'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/documentation',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/documentation', '454'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/docusaurus',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/docusaurus', '067'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/domain',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/domain', 'a68'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/domain-tag',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/domain-tag', '961'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/error-recovery',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/error-recovery', '209'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/executable',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/executable', '569'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/git',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/git', '4c7'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/github',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/github', '1b6'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/guide',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/guide', 'fea'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/hub',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/hub', 'a1b'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/implementation',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/implementation', 'dc1'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/incident-response',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/incident-response', '8b5'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/lessons-learned',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/lessons-learned', 'c9a'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/maintenance',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/maintenance', 'c6c'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/migration',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/migration', 'dcd'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/milestone',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/milestone', 'd13'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/milestones',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/milestones', '2b0'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/monorepo',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/monorepo', '507'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/navigation',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/navigation', 'dc6'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/optimization',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/optimization', '05e'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/process',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/process', 'cd3'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/process-improvement',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/process-improvement', 'f4c'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/progress',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/progress', 'c52'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/quality',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/quality', '2c6'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/reference',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/reference', '448'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/requirements',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/requirements', 'd2f'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/results',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/results', 'b1c'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/rollback',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/rollback', 'f83'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/standards',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/standards', '76e'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/structure',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/structure', 'b2d'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/template',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/template', 'ea2'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/test',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/test', '9e7'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/testing',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/testing', '52b'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/typescript',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/typescript', '2ce'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/validation',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/validation', 'd0e'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/work-log',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/work-log', '49a'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs/tags/workflow',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs/tags/workflow', '585'),
    exact: true
  },
  {
    path: '/kloudi-swe-agent/tech-specs',
    component: ComponentCreator('/kloudi-swe-agent/tech-specs', '7ef'),
    routes: [
      {
        path: '/kloudi-swe-agent/tech-specs/adrs/adr-001-monorepo',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/adr-001-monorepo', '8e3'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/adrs/adr-002-typescript',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/adr-002-typescript', '59e'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/adrs/adr-003-jsonld',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/adr-003-jsonld', '7eb'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/adrs/adr-007-docusaurus',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/adr-007-docusaurus', 'a3e'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/adrs/log',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/adrs/log', '5c9'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/dependencies',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/dependencies', '2ea'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/guides/agent-configuration-guide',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/guides/agent-configuration-guide', 'a3d'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/log',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/log', '852'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/milestone-M0',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-M0', '363'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/milestone-M0.1',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-M0.1', '36f'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/milestone-M0.2',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-M0.2', 'fc4'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/milestone-M1',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-M1', 'f80'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/milestone-TEST',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/milestone-TEST', '91d'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/conversation-summary',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/conversation-summary', '1ca'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/fixes-checklist',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/fixes-checklist', '0fd'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/implementation-log',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/implementation-log', 'b97'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/technical-reference',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/milestones/work-log/milestone-m0/technical-reference', '316'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/', '5a1'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/agent-rules/augment',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/augment', '9db'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/agent-rules/claude',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/claude', '401'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/agent-rules/copilot',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/copilot', '772'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/agent-rules/core',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/core', 'a6d'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/agent-rules/cursor',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/cursor', '47d'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/agent-rules/custom',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/custom', 'c5a'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/agent-rules/validation',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/agent-rules/validation', 'bca'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/core/architectural-decisions',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/architectural-decisions', 'cd3'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/core/documentation',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/documentation', '0c3'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/core/error-recovery',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/error-recovery', '48e'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/core/git-workflow',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/git-workflow', 'f96'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/core/milestone-implementation',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/milestone-implementation', '334'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/core/quality-assurance',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/core/quality-assurance', 'ccd'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/migration-guide',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/migration-guide', '206'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/optimisation-results/optimisation-result-001-results',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/optimisation-results/optimisation-result-001-results', '3ae'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/templates/adr-template',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/adr-template', 'f5c'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/templates/domain-template',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/domain-template', 'd20'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/templates/milestone-template',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/milestone-template', 'dd5'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/templates/process-improvement',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/process-improvement', '7c9'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/templates/requirement-checklist',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/requirement-checklist', 'f96'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/process/templates/work-log-template',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/process/templates/work-log-template', '287'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/spec_checklist',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/spec_checklist', '19a'),
        exact: true
      },
      {
        path: '/kloudi-swe-agent/tech-specs/structure',
        component: ComponentCreator('/kloudi-swe-agent/tech-specs/structure', '36e'),
        exact: true,
        sidebar: "techSpecsSidebar"
      }
    ]
  },
  {
    path: '/kloudi-swe-agent/',
    component: ComponentCreator('/kloudi-swe-agent/', '0e0'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
