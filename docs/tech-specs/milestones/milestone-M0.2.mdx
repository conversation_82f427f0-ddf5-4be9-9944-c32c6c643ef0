---
title: Milestone M0.2 — Remove Agent Dry-Run References
description: Clean up all traces of agent dry-run functionality from the repository, including package.json scripts, CI workflows, documentation, and work logs.
created: 2025-01-25
version: 0.1.0
status: Draft
tags: [milestone, cleanup, maintenance]
authors: [nitishMehrotra]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🧹">
<strong>Goal:</strong> Remove all references to the non-existent agent dry-run functionality from the entire repository,
ensuring clean CI pipelines and accurate documentation. This milestone addresses the missing <code>scripts/agent-dry-run.mjs</code>
file that was referenced but never implemented.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"
pnpm: "8.15.4"
git: "latest"
grep: "system default"
sed: "system default"
```

---

## 🎯 Definition of Done

1. All references to `agent:dry-run`, `agent-dry-run`, and `dry-run` removed from codebase.
2. CI pipeline runs successfully without dry-run step.
3. Package.json scripts cleaned of non-existent script references.
4. Documentation updated to reflect current validation approach.
5. Work logs updated to reflect accurate implementation status.

---

## 📦 Deliverables

| Path | Must contain … |
|------|----------------|
| `code/package.json` | **No `agent:dry-run` script** in scripts section |
| `code/.github/workflows/ci.yml` | **No `pnpm run agent:dry-run` step** in workflow |
| `docs/tech-specs/milestones/milestone-M0.mdx` | **Updated acceptance tests** without dry-run reference |
| `docs/tech-specs/spec_checklist.mdx` | **Removed dry-run validation commands** |
| `docs/tech-specs/milestones/work-log/milestone-m0/` | **Updated logs** reflecting accurate implementation status |
| `docs/tech-specs/milestones/work-log/milestone-m0/technical-reference.md` | **Removed dry-run script documentation** |

---

## 🗂 Directory Layout

```text
Files to be modified:
├── code/package.json                                    # Remove agent:dry-run script
├── code/.github/workflows/ci.yml                        # Remove dry-run CI step
├── docs/tech-specs/milestones/milestone-M0.mdx          # Update acceptance tests
├── docs/tech-specs/spec_checklist.mdx                   # Remove dry-run validation
├── docs/tech-specs/milestones/work-log/milestone-m0/
│   ├── implementation-log.md                            # Update implementation status
│   └── technical-reference.md                           # Remove dry-run documentation
└── docs/scripts/acceptance/m0-acceptance.sh             # Remove dry-run test step

Files to verify (no changes needed):
├── docs/tech-specs/milestones/milestone-M0.1.mdx        # Already clean
└── docs/scripts/acceptance/m0.1-acceptance.sh           # Already clean
```

---

## 🧠 Key Decisions

| Topic | Decision | Rationale |
|-------|----------|-----------|
| **Cleanup Scope** | **Complete removal** | Agent dry-run script was never implemented; references cause CI failures |
| **Validation Strategy** | **Use existing tools** | Spec-lint and acceptance tests provide sufficient validation |
| **Documentation Update** | **Accurate reflection** | Work logs should reflect actual implementation, not planned features |
| **CI Pipeline** | **Streamlined workflow** | Remove non-functional step to ensure reliable CI |

---

## ✅ Success Criteria

**AUTHORITATIVE TEST**: `bash docs/scripts/acceptance/m0.2-acceptance.sh`

The milestone is complete when the acceptance script passes (exit code 0). This script validates all dry-run references have been removed.

**The script validates:**
- [ ] SC-1 No `agent:dry-run` script in `code/package.json`
- [ ] SC-2 No `pnpm run agent:dry-run` in CI workflow
- [ ] SC-3 No dry-run references in milestone M0 documentation
- [ ] SC-4 No dry-run validation commands in spec checklist
- [ ] SC-5 Updated work logs reflect accurate implementation status
- [ ] SC-6 CI pipeline runs successfully without dry-run step
- [ ] SC-7 All existing acceptance tests still pass

---

## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation. These rules prevent common implementation issues and ensure quality standards.

> **📋 Complete Process Guidelines:** See [Core Process Guidelines](../process/agent-rules/core.mdx) for comprehensive milestone implementation, quality assurance, and agent configuration processes.

**Key Requirements for this milestone:**
- **Systematic Search**: Use grep/find to locate ALL references before making changes
- **Validation First**: Verify current CI failure before and success after changes
- **Documentation Accuracy**: Ensure work logs reflect actual vs. planned implementation
- **Incremental Testing**: Test CI pipeline after each major change

---

## 🔨 Task Breakdown

| #  | Branch name           | Task (issue title)                            | Owner | Hint |
|----|----------------------|-----------------------------------------------|-------|------|
| 01 | `m0.2/audit-refs`     | Audit all dry-run references in codebase     | Dev   | `grep -r "dry-run" .` |
| 02 | `m0.2/clean-package`  | Remove agent:dry-run from package.json       | Dev   | Edit scripts section |
| 03 | `m0.2/clean-ci`       | Remove dry-run step from CI workflow         | DevOps| Edit .github/workflows/ci.yml |
| 04 | `m0.2/clean-m0-docs`  | Update milestone M0 acceptance tests         | Dev   | Remove test 6 from embedded script |
| 05 | `m0.2/clean-checklist`| Remove dry-run from spec checklist           | Dev   | Edit validation commands section |
| 06 | `m0.2/update-worklogs`| Update work logs with accurate status        | Dev   | Mark dry-run as "NOT IMPLEMENTED" |
| 07 | `m0.2/clean-techref`  | Remove dry-run from technical reference      | Dev   | Edit POST-IMPLEMENTATION section |
| 08 | `m0.2/create-acceptance`| Create M0.2 acceptance test script         | Dev   | Validate all changes |
| 09 | `m0.2/test-ci`        | Verify CI pipeline runs successfully         | DevOps| Ensure no failures |
| 10 | `m0.2/final-validation`| Run all existing acceptance tests           | QA    | Ensure no regressions |

---

## 🤖 CI Pipeline

```yaml
# No changes to CI pipeline structure needed
# This milestone will REMOVE the problematic dry-run step
# Existing CI workflow will continue to work after cleanup
```

---

## 🧪 Acceptance Tests

### 1️⃣ Verify no dry-run references

```bash
# Should return no results
grep -r "agent:dry-run\|agent-dry-run\|dry-run" . --exclude-dir=node_modules --exclude-dir=.git
```

### 2️⃣ Package.json validation

```bash
# Should not contain agent:dry-run
! grep -q "agent:dry-run" code/package.json
```

### 3️⃣ CI workflow validation

```bash
# Should not contain dry-run step
! grep -q "agent:dry-run" code/.github/workflows/ci.yml
```

### 4️⃣ Existing tests still pass

```bash
# All existing acceptance tests should continue to work
bash docs/scripts/acceptance/m0-acceptance.sh
```

---

## 🔄 Document History

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 0.1.0 | 2025-01-25 | nitishMehrotra | Initial milestone specification for dry-run cleanup |

---

When all criteria pass, merge to **`main`**, tag **`cleanup-v0.2.0`**, and mark Milestone M0.2 closed.
