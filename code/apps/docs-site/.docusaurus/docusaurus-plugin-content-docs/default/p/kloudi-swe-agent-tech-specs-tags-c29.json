{"tags": [{"label": "adr", "permalink": "/kloudi-swe-agent/tech-specs/tags/adr", "count": 6}, {"label": "architecture", "permalink": "/kloudi-swe-agent/tech-specs/tags/architecture", "count": 7}, {"label": "monorepo", "permalink": "/kloudi-swe-agent/tech-specs/tags/monorepo", "count": 1}, {"label": "typescript", "permalink": "/kloudi-swe-agent/tech-specs/tags/typescript", "count": 1}, {"label": "data-format", "permalink": "/kloudi-swe-agent/tech-specs/tags/data-format", "count": 1}, {"label": "documentation", "permalink": "/kloudi-swe-agent/tech-specs/tags/documentation", "count": 3}, {"label": "<PERSON>cusaurus", "permalink": "/kloudi-swe-agent/tech-specs/tags/docusaurus", "count": 1}, {"label": "decisions", "permalink": "/kloudi-swe-agent/tech-specs/tags/decisions", "count": 2}, {"label": "process", "permalink": "/kloudi-swe-agent/tech-specs/tags/process", "count": 8}, {"label": "agents", "permalink": "/kloudi-swe-agent/tech-specs/tags/agents", "count": 1}, {"label": "configuration", "permalink": "/kloudi-swe-agent/tech-specs/tags/configuration", "count": 1}, {"label": "quality", "permalink": "/kloudi-swe-agent/tech-specs/tags/quality", "count": 2}, {"label": "milestones", "permalink": "/kloudi-swe-agent/tech-specs/tags/milestones", "count": 1}, {"label": "progress", "permalink": "/kloudi-swe-agent/tech-specs/tags/progress", "count": 1}, {"label": "milestone", "permalink": "/kloudi-swe-agent/tech-specs/tags/milestone", "count": 7}, {"label": "cleanup", "permalink": "/kloudi-swe-agent/tech-specs/tags/cleanup", "count": 1}, {"label": "maintenance", "permalink": "/kloudi-swe-agent/tech-specs/tags/maintenance", "count": 1}, {"label": "test", "permalink": "/kloudi-swe-agent/tech-specs/tags/test", "count": 1}, {"label": "validation", "permalink": "/kloudi-swe-agent/tech-specs/tags/validation", "count": 6}, {"label": "agent-configuration", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-configuration", "count": 3}, {"label": "agent-rules", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-rules", "count": 7}, {"label": "augment", "permalink": "/kloudi-swe-agent/tech-specs/tags/augment", "count": 1}, {"label": "executable", "permalink": "/kloudi-swe-agent/tech-specs/tags/executable", "count": 6}, {"label": "claude", "permalink": "/kloudi-swe-agent/tech-specs/tags/claude", "count": 1}, {"label": "anthropic", "permalink": "/kloudi-swe-agent/tech-specs/tags/anthropic", "count": 1}, {"label": "copilot", "permalink": "/kloudi-swe-agent/tech-specs/tags/copilot", "count": 1}, {"label": "github", "permalink": "/kloudi-swe-agent/tech-specs/tags/github", "count": 1}, {"label": "core", "permalink": "/kloudi-swe-agent/tech-specs/tags/core", "count": 1}, {"label": "cursor", "permalink": "/kloudi-swe-agent/tech-specs/tags/cursor", "count": 1}, {"label": "custom", "permalink": "/kloudi-swe-agent/tech-specs/tags/custom", "count": 1}, {"label": "template", "permalink": "/kloudi-swe-agent/tech-specs/tags/template", "count": 4}, {"label": "testing", "permalink": "/kloudi-swe-agent/tech-specs/tags/testing", "count": 2}, {"label": "standards", "permalink": "/kloudi-swe-agent/tech-specs/tags/standards", "count": 1}, {"label": "error-recovery", "permalink": "/kloudi-swe-agent/tech-specs/tags/error-recovery", "count": 1}, {"label": "rollback", "permalink": "/kloudi-swe-agent/tech-specs/tags/rollback", "count": 1}, {"label": "incident-response", "permalink": "/kloudi-swe-agent/tech-specs/tags/incident-response", "count": 1}, {"label": "git", "permalink": "/kloudi-swe-agent/tech-specs/tags/git", "count": 1}, {"label": "workflow", "permalink": "/kloudi-swe-agent/tech-specs/tags/workflow", "count": 1}, {"label": "branching", "permalink": "/kloudi-swe-agent/tech-specs/tags/branching", "count": 1}, {"label": "implementation", "permalink": "/kloudi-swe-agent/tech-specs/tags/implementation", "count": 1}, {"label": "migration", "permalink": "/kloudi-swe-agent/tech-specs/tags/migration", "count": 1}, {"label": "guide", "permalink": "/kloudi-swe-agent/tech-specs/tags/guide", "count": 1}, {"label": "optimization", "permalink": "/kloudi-swe-agent/tech-specs/tags/optimization", "count": 1}, {"label": "results", "permalink": "/kloudi-swe-agent/tech-specs/tags/results", "count": 1}, {"label": "navigation", "permalink": "/kloudi-swe-agent/tech-specs/tags/navigation", "count": 1}, {"label": "hub", "permalink": "/kloudi-swe-agent/tech-specs/tags/hub", "count": 1}, {"label": "domain", "permalink": "/kloudi-swe-agent/tech-specs/tags/domain", "count": 1}, {"label": "<domain-tag>", "permalink": "/kloudi-swe-agent/tech-specs/tags/domain-tag", "count": 1}, {"label": "process-improvement", "permalink": "/kloudi-swe-agent/tech-specs/tags/process-improvement", "count": 1}, {"label": "lessons-learned", "permalink": "/kloudi-swe-agent/tech-specs/tags/lessons-learned", "count": 1}, {"label": "agent-ready", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-ready", "count": 3}, {"label": "checklist", "permalink": "/kloudi-swe-agent/tech-specs/tags/checklist", "count": 2}, {"label": "requirements", "permalink": "/kloudi-swe-agent/tech-specs/tags/requirements", "count": 1}, {"label": "work-log", "permalink": "/kloudi-swe-agent/tech-specs/tags/work-log", "count": 1}, {"label": "reference", "permalink": "/kloudi-swe-agent/tech-specs/tags/reference", "count": 1}, {"label": "structure", "permalink": "/kloudi-swe-agent/tech-specs/tags/structure", "count": 1}]}