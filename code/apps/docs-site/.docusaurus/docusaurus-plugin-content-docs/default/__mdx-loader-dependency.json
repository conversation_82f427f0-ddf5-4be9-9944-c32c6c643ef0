{"options": {"path": "../../../docs/tech-specs", "routeBasePath": "/tech-specs", "sidebarPath": "/Users/<USER>/tmp/kloudi-swe-agent/code/apps/docs-site/sidebars.js", "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/", "editCurrentVersion": false, "editLocalizedFiles": false, "tagsBasePath": "tags", "include": ["**/*.{md,mdx}"], "exclude": ["**/_*.{js,jsx,ts,tsx,md,mdx}", "**/_*/**", "**/*.test.{js,jsx,ts,tsx}", "**/__tests__/**"], "sidebarCollapsible": true, "sidebarCollapsed": true, "docsRootComponent": "@theme/DocsRoot", "docVersionRootComponent": "@theme/DocVersionRoot", "docRootComponent": "@theme/DocRoot", "docItemComponent": "@theme/DocItem", "docTagsListComponent": "@theme/DocTagsListPage", "docTagDocListComponent": "@theme/DocTagDocListPage", "docCategoryGeneratedIndexComponent": "@theme/DocCategoryGeneratedIndexPage", "remarkPlugins": [], "rehypePlugins": [], "recmaPlugins": [], "beforeDefaultRemarkPlugins": [], "beforeDefaultRehypePlugins": [], "admonitions": true, "showLastUpdateTime": false, "showLastUpdateAuthor": false, "includeCurrentVersion": true, "disableVersioning": false, "versions": {}, "breadcrumbs": true, "onInlineTags": "warn", "id": "default"}, "versionsMetadata": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/kloudi-swe-agent/tech-specs", "tagsPath": "/kloudi-swe-agent/tech-specs/tags", "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs", "editUrlLocalized": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/i18n/en/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Users/<USER>/tmp/kloudi-swe-agent/code/apps/docs-site/sidebars.js", "contentPath": "/Users/<USER>/tmp/kloudi-swe-agent/docs/tech-specs", "contentPathLocalized": "/Users/<USER>/tmp/kloudi-swe-agent/code/apps/docs-site/i18n/en/docusaurus-plugin-content-docs/current"}]}