{"tag": {"label": "template", "permalink": "/kloudi-swe-agent/tech-specs/tags/template", "allTagsPath": "/kloudi-swe-agent/tech-specs/tags", "count": 4, "items": [{"id": "process/agent-rules/custom", "title": "Custom Agent Configuration Template", "description": "Template for configuring custom AI agents for milestone implementation", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/custom"}, {"id": "process/templates/process-improvement", "title": "Process Improvement Template", "description": "Streamlined template for capturing process improvements and lessons learned", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/process-improvement"}, {"id": "process/templates/requirement-checklist", "title": "Requirement Checklist Template", "description": "Streamlined pre-implementation validation checklist", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/requirement-checklist"}, {"id": "process/templates/work-log-template", "title": "Work Log Template", "description": "Streamlined template for milestone implementation work logs", "permalink": "/kloudi-swe-agent/tech-specs/process/templates/work-log-template"}], "unlisted": false}}