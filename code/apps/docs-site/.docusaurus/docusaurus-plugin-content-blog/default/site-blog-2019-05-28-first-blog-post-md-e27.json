{"permalink": "/kloudi-swe-agent/blog/first-blog-post", "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/blog/blog/2019-05-28-first-blog-post.md", "source": "@site/blog/2019-05-28-first-blog-post.md", "title": "First Blog Post", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet", "date": "2019-05-28T00:00:00.000Z", "tags": [{"inline": true, "label": "hola", "permalink": "/kloudi-swe-agent/blog/tags/hola"}, {"inline": true, "label": "<PERSON>cusaurus", "permalink": "/kloudi-swe-agent/blog/tags/docusaurus"}], "readingTime": 0.12, "hasTruncateMarker": false, "authors": [{"name": "<PERSON>", "title": "Docusaurus Core Team", "url": "https://github.com/wgao19", "image_url": "https://github.com/wgao19.png", "imageURL": "https://github.com/wgao19.png", "socials": {}, "key": null, "page": null}], "frontMatter": {"slug": "first-blog-post", "title": "First Blog Post", "authors": {"name": "<PERSON>", "title": "Docusaurus Core Team", "url": "https://github.com/wgao19", "image_url": "https://github.com/wgao19.png", "imageURL": "https://github.com/wgao19.png"}, "tags": ["hola", "<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Long Blog Post", "permalink": "/kloudi-swe-agent/blog/long-blog-post"}}