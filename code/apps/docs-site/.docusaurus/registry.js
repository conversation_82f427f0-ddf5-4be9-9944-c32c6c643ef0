export default {
  "01a85c17": [() => import(/* webpackChunkName: "01a85c17" */ "@theme/BlogTagsListPage"), "@theme/BlogTagsListPage", require.resolveWeak("@theme/BlogTagsListPage")],
  "023c3bee": [() => import(/* webpackChunkName: "023c3bee" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-work-log-d54.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-work-log-d54.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-work-log-d54.json")],
  "05095f0e": [() => import(/* webpackChunkName: "05095f0e" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-cleanup-3e5.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-cleanup-3e5.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-cleanup-3e5.json")],
  "06b5fcb5": [() => import(/* webpackChunkName: "06b5fcb5" */ "@site/../../../docs/tech-specs/adrs/adr-007-docusaurus.mdx"), "@site/../../../docs/tech-specs/adrs/adr-007-docusaurus.mdx", require.resolveWeak("@site/../../../docs/tech-specs/adrs/adr-007-docusaurus.mdx")],
  "0779dfc3": [() => import(/* webpackChunkName: "0779dfc3" */ "@site/../../../docs/tech-specs/milestones/milestone-M0.2.mdx"), "@site/../../../docs/tech-specs/milestones/milestone-M0.2.mdx", require.resolveWeak("@site/../../../docs/tech-specs/milestones/milestone-M0.2.mdx")],
  "07eb2fcf": [() => import(/* webpackChunkName: "07eb2fcf" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-configuration-8d0.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-configuration-8d0.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-configuration-8d0.json")],
  "088259e9": [() => import(/* webpackChunkName: "088259e9" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-rollback-1f4.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-rollback-1f4.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-rollback-1f4.json")],
  "09befa9a": [() => import(/* webpackChunkName: "09befa9a" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-architecture-b79.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-architecture-b79.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-architecture-b79.json")],
  "0ae7f8aa": [() => import(/* webpackChunkName: "0ae7f8aa" */ "@site/../../../docs/tech-specs/process/agent-rules/core.mdx"), "@site/../../../docs/tech-specs/process/agent-rules/core.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/agent-rules/core.mdx")],
  "0b996d24": [() => import(/* webpackChunkName: "0b996d24" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-structure-cb5.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-structure-cb5.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-structure-cb5.json")],
  "0b9cb956": [() => import(/* webpackChunkName: "0b9cb956" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-template-3cc.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-template-3cc.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-template-3cc.json")],
  "1205d24c": [() => import(/* webpackChunkName: "1205d24c" */ "@site/../../../docs/tech-specs/guides/agent-configuration-guide.mdx"), "@site/../../../docs/tech-specs/guides/agent-configuration-guide.mdx", require.resolveWeak("@site/../../../docs/tech-specs/guides/agent-configuration-guide.mdx")],
  "138e0e15": [() => import(/* webpackChunkName: "138e0e15" */ "@generated/@easyops-cn/docusaurus-search-local/default/__plugin.json"), "@generated/@easyops-cn/docusaurus-search-local/default/__plugin.json", require.resolveWeak("@generated/@easyops-cn/docusaurus-search-local/default/__plugin.json")],
  "16129eb8": [() => import(/* webpackChunkName: "16129eb8" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-implementation-075.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-implementation-075.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-implementation-075.json")],
  "17896441": [() => import(/* webpackChunkName: "17896441" */ "@theme/DocItem"), "@theme/DocItem", require.resolveWeak("@theme/DocItem")],
  "1a4e3797": [() => import(/* webpackChunkName: "1a4e3797" */ "@theme/SearchPage"), "@theme/SearchPage", require.resolveWeak("@theme/SearchPage")],
  "1af255a7": [() => import(/* webpackChunkName: "1af255a7" */ "@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/conversation-summary.md"), "@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/conversation-summary.md", require.resolveWeak("@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/conversation-summary.md")],
  "1c87e086": [() => import(/* webpackChunkName: "1c87e086" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-migration-e03.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-migration-e03.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-migration-e03.json")],
  "1dbee74b": [() => import(/* webpackChunkName: "1dbee74b" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-monorepo-09f.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-monorepo-09f.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-monorepo-09f.json")],
  "1df93b7f": [() => import(/* webpackChunkName: "1df93b7f" */ "@site/src/pages/index.tsx"), "@site/src/pages/index.tsx", require.resolveWeak("@site/src/pages/index.tsx")],
  "1f391b9e": [() => import(/* webpackChunkName: "1f391b9e" */ "@theme/MDXPage"), "@theme/MDXPage", require.resolveWeak("@theme/MDXPage")],
  "214cb097": [() => import(/* webpackChunkName: "214cb097" */ "@site/../../../docs/tech-specs/process/agent-rules/custom.mdx"), "@site/../../../docs/tech-specs/process/agent-rules/custom.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/agent-rules/custom.mdx")],
  "2158b9bc": [() => import(/* webpackChunkName: "2158b9bc" */ "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-hello-85f.json"), "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-hello-85f.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-hello-85f.json")],
  "25a99ffb": [() => import(/* webpackChunkName: "25a99ffb" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-git-386.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-git-386.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-git-386.json")],
  "2639e6c1": [() => import(/* webpackChunkName: "2639e6c1" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-cursor-394.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-cursor-394.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-cursor-394.json")],
  "28057cff": [() => import(/* webpackChunkName: "28057cff" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-progress-92c.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-progress-92c.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-progress-92c.json")],
  "29b88979": [() => import(/* webpackChunkName: "29b88979" */ "@site/../../../docs/tech-specs/spec_checklist.mdx"), "@site/../../../docs/tech-specs/spec_checklist.mdx", require.resolveWeak("@site/../../../docs/tech-specs/spec_checklist.mdx")],
  "2dc6156f": [() => import(/* webpackChunkName: "2dc6156f" */ "@site/../../../docs/tech-specs/process/templates/work-log-template.mdx"), "@site/../../../docs/tech-specs/process/templates/work-log-template.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/templates/work-log-template.mdx")],
  "2f69ef47": [() => import(/* webpackChunkName: "2f69ef47" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-branching-1a7.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-branching-1a7.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-branching-1a7.json")],
  "3051e210": [() => import(/* webpackChunkName: "3051e210" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-domain-dc5.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-domain-dc5.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-domain-dc5.json")],
  "343fac29": [() => import(/* webpackChunkName: "343fac29" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-core-b31.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-core-b31.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-core-b31.json")],
  "36994c47": [() => import(/* webpackChunkName: "36994c47" */ "@generated/docusaurus-plugin-content-blog/default/__plugin.json"), "@generated/docusaurus-plugin-content-blog/default/__plugin.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/__plugin.json")],
  "36de0825": [() => import(/* webpackChunkName: "36de0825" */ "@site/../../../docs/tech-specs/milestones/milestone-TEST.mdx"), "@site/../../../docs/tech-specs/milestones/milestone-TEST.mdx", require.resolveWeak("@site/../../../docs/tech-specs/milestones/milestone-TEST.mdx")],
  "3712e873": [() => import(/* webpackChunkName: "3712e873" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-maintenance-a3d.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-maintenance-a3d.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-maintenance-a3d.json")],
  "3720c009": [() => import(/* webpackChunkName: "3720c009" */ "@theme/DocTagsListPage"), "@theme/DocTagsListPage", require.resolveWeak("@theme/DocTagsListPage")],
  "393be207": [() => import(/* webpackChunkName: "393be207" */ "@site/src/pages/markdown-page.md"), "@site/src/pages/markdown-page.md", require.resolveWeak("@site/src/pages/markdown-page.md")],
  "3a726a0d": [() => import(/* webpackChunkName: "3a726a0d" */ "@site/../../../docs/tech-specs/process/core/milestone-implementation.mdx"), "@site/../../../docs/tech-specs/process/core/milestone-implementation.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/core/milestone-implementation.mdx")],
  "3e95cb9e": [() => import(/* webpackChunkName: "3e95cb9e" */ "@site/../../../docs/tech-specs/process/README.mdx"), "@site/../../../docs/tech-specs/process/README.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/README.mdx")],
  "41a3b77a": [() => import(/* webpackChunkName: "41a3b77a" */ "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-c3d.json"), "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-c3d.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-c3d.json")],
  "41cfea20": [() => import(/* webpackChunkName: "41cfea20" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-test-9a6.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-test-9a6.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-test-9a6.json")],
  "465ede43": [() => import(/* webpackChunkName: "465ede43" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-process-6d2.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-process-6d2.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-process-6d2.json")],
  "47733394": [() => import(/* webpackChunkName: "47733394" */ "@site/../../../docs/tech-specs/process/agent-rules/cursor.mdx"), "@site/../../../docs/tech-specs/process/agent-rules/cursor.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/agent-rules/cursor.mdx")],
  "4b811217": [() => import(/* webpackChunkName: "4b811217" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-typescript-d3c.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-typescript-d3c.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-typescript-d3c.json")],
  "4f1721e1": [() => import(/* webpackChunkName: "4f1721e1" */ "@site/../../../docs/tech-specs/adrs/adr-003-jsonld.mdx"), "@site/../../../docs/tech-specs/adrs/adr-003-jsonld.mdx", require.resolveWeak("@site/../../../docs/tech-specs/adrs/adr-003-jsonld.mdx")],
  "4fea78a4": [() => import(/* webpackChunkName: "4fea78a4" */ "@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/fixes-checklist.md"), "@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/fixes-checklist.md", require.resolveWeak("@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/fixes-checklist.md")],
  "5017d2e7": [() => import(/* webpackChunkName: "5017d2e7" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agent-configuration-04b.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agent-configuration-04b.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agent-configuration-04b.json")],
  "50c89f46": [() => import(/* webpackChunkName: "50c89f46" */ "@site/../../../docs/tech-specs/milestones/log.mdx"), "@site/../../../docs/tech-specs/milestones/log.mdx", require.resolveWeak("@site/../../../docs/tech-specs/milestones/log.mdx")],
  "516308ea": [() => import(/* webpackChunkName: "516308ea" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-custom-9fe.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-custom-9fe.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-custom-9fe.json")],
  "523fff21": [() => import(/* webpackChunkName: "523fff21" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-workflow-7a8.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-workflow-7a8.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-workflow-7a8.json")],
  "5417fd8e": [() => import(/* webpackChunkName: "5417fd8e" */ "@site/../../../docs/tech-specs/process/optimisation-results/optimisation-result-001-results.mdx"), "@site/../../../docs/tech-specs/process/optimisation-results/optimisation-result-001-results.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/optimisation-results/optimisation-result-001-results.mdx")],
  "56c4ea2d": [() => import(/* webpackChunkName: "56c4ea2d" */ "@site/../../../docs/tech-specs/process/templates/requirement-checklist.mdx"), "@site/../../../docs/tech-specs/process/templates/requirement-checklist.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/templates/requirement-checklist.mdx")],
  "58f0080e": [() => import(/* webpackChunkName: "58f0080e" */ "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-hola-f10.json"), "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-hola-f10.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-hola-f10.json")],
  "58fc6d8c": [() => import(/* webpackChunkName: "58fc6d8c" */ "@site/../../../docs/tech-specs/process/templates/adr-template.mdx"), "@site/../../../docs/tech-specs/process/templates/adr-template.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/templates/adr-template.mdx")],
  "59362658": [() => import(/* webpackChunkName: "59362658" */ "@site/blog/2021-08-01-mdx-blog-post.mdx"), "@site/blog/2021-08-01-mdx-blog-post.mdx", require.resolveWeak("@site/blog/2021-08-01-mdx-blog-post.mdx")],
  "5c8bb66b": [() => import(/* webpackChunkName: "5c8bb66b" */ "@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/implementation-log.md"), "@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/implementation-log.md", require.resolveWeak("@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/implementation-log.md")],
  "5d9285f8": [() => import(/* webpackChunkName: "5d9285f8" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-adr-8bc.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-adr-8bc.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-adr-8bc.json")],
  "5db6fa06": [() => import(/* webpackChunkName: "5db6fa06" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agent-ready-149.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agent-ready-149.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agent-ready-149.json")],
  "5e95c892": [() => import(/* webpackChunkName: "5e95c892" */ "@theme/DocsRoot"), "@theme/DocsRoot", require.resolveWeak("@theme/DocsRoot")],
  "5e9f5e1a": [() => import(/* webpackChunkName: "5e9f5e1a" */ "@generated/docusaurus.config"), "@generated/docusaurus.config", require.resolveWeak("@generated/docusaurus.config")],
  "621db11d": [() => import(/* webpackChunkName: "621db11d" */ "@theme/Blog/Pages/BlogAuthorsListPage"), "@theme/Blog/Pages/BlogAuthorsListPage", require.resolveWeak("@theme/Blog/Pages/BlogAuthorsListPage")],
  "64b0bff7": [() => import(/* webpackChunkName: "64b0bff7" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-standards-568.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-standards-568.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-standards-568.json")],
  "6875c492": [() => import(/* webpackChunkName: "6875c492" */ "@theme/BlogTagsPostsPage"), "@theme/BlogTagsPostsPage", require.resolveWeak("@theme/BlogTagsPostsPage")],
  "6939f321": [() => import(/* webpackChunkName: "6939f321" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-executable-d6c.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-executable-d6c.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-executable-d6c.json")],
  "6b499565": [() => import(/* webpackChunkName: "6b499565" */ "@site/../../../docs/tech-specs/structure.mdx"), "@site/../../../docs/tech-specs/structure.mdx", require.resolveWeak("@site/../../../docs/tech-specs/structure.mdx")],
  "6c8b1ffe": [() => import(/* webpackChunkName: "6c8b1ffe" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-incident-response-c82.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-incident-response-c82.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-incident-response-c82.json")],
  "6cd3a649": [() => import(/* webpackChunkName: "6cd3a649" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-optimization-559.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-optimization-559.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-optimization-559.json")],
  "6e501e55": [() => import(/* webpackChunkName: "6e501e55" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-data-format-e22.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-data-format-e22.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-data-format-e22.json")],
  "73664a40": [() => import(/* webpackChunkName: "73664a40" */ "@site/blog/2019-05-29-long-blog-post.md"), "@site/blog/2019-05-29-long-blog-post.md", require.resolveWeak("@site/blog/2019-05-29-long-blog-post.md")],
  "7661071f": [() => import(/* webpackChunkName: "7661071f" */ "@site/blog/2021-08-26-welcome/index.md?truncated=true"), "@site/blog/2021-08-26-welcome/index.md?truncated=true", require.resolveWeak("@site/blog/2021-08-26-welcome/index.md?truncated=true")],
  "77a2f337": [() => import(/* webpackChunkName: "77a2f337" */ "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-facebook-cad.json"), "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-facebook-cad.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-facebook-cad.json")],
  "80c91c3a": [() => import(/* webpackChunkName: "80c91c3a" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-claude-742.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-claude-742.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-claude-742.json")],
  "80d99499": [() => import(/* webpackChunkName: "80d99499" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-error-recovery-b7f.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-error-recovery-b7f.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-error-recovery-b7f.json")],
  "80dcc677": [() => import(/* webpackChunkName: "80dcc677" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-requirements-333.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-requirements-333.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-requirements-333.json")],
  "80f49984": [() => import(/* webpackChunkName: "80f49984" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-documentation-292.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-documentation-292.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-documentation-292.json")],
  "814f3328": [() => import(/* webpackChunkName: "814f3328" */ "~blog/default/blog-post-list-prop-default.json"), "~blog/default/blog-post-list-prop-default.json", require.resolveWeak("~blog/default/blog-post-list-prop-default.json")],
  "853d612a": [() => import(/* webpackChunkName: "853d612a" */ "@site/../../../docs/tech-specs/process/migration-guide.mdx"), "@site/../../../docs/tech-specs/process/migration-guide.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/migration-guide.mdx")],
  "8717b14a": [() => import(/* webpackChunkName: "8717b14a" */ "@site/blog/2019-05-29-long-blog-post.md?truncated=true"), "@site/blog/2019-05-29-long-blog-post.md?truncated=true", require.resolveWeak("@site/blog/2019-05-29-long-blog-post.md?truncated=true")],
  "8a78d9b8": [() => import(/* webpackChunkName: "8a78d9b8" */ "@site/../../../docs/tech-specs/adrs/log.mdx"), "@site/../../../docs/tech-specs/adrs/log.mdx", require.resolveWeak("@site/../../../docs/tech-specs/adrs/log.mdx")],
  "8e5d5ac1": [() => import(/* webpackChunkName: "8e5d5ac1" */ "@site/../../../docs/tech-specs/process/agent-rules/augment.mdx"), "@site/../../../docs/tech-specs/process/agent-rules/augment.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/agent-rules/augment.mdx")],
  "90a9d321": [() => import(/* webpackChunkName: "90a9d321" */ "@site/../../../docs/tech-specs/process/core/quality-assurance.mdx"), "@site/../../../docs/tech-specs/process/core/quality-assurance.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/core/quality-assurance.mdx")],
  "923182d6": [() => import(/* webpackChunkName: "923182d6" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-19e.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-19e.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-19e.json")],
  "925b3f96": [() => import(/* webpackChunkName: "925b3f96" */ "@site/blog/2019-05-28-first-blog-post.md?truncated=true"), "@site/blog/2019-05-28-first-blog-post.md?truncated=true", require.resolveWeak("@site/blog/2019-05-28-first-blog-post.md?truncated=true")],
  "93e2df58": [() => import(/* webpackChunkName: "93e2df58" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-quality-b36.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-quality-b36.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-quality-b36.json")],
  "953e8e74": [() => import(/* webpackChunkName: "953e8e74" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-milestone-ab1.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-milestone-ab1.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-milestone-ab1.json")],
  "9623166c": [() => import(/* webpackChunkName: "9623166c" */ "@site/../../../docs/tech-specs/dependencies.mdx"), "@site/../../../docs/tech-specs/dependencies.mdx", require.resolveWeak("@site/../../../docs/tech-specs/dependencies.mdx")],
  "97120de4": [() => import(/* webpackChunkName: "97120de4" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-lessons-learned-875.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-lessons-learned-875.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-lessons-learned-875.json")],
  "9800ebae": [() => import(/* webpackChunkName: "9800ebae" */ "@site/../../../docs/tech-specs/adrs/adr-001-monorepo.mdx"), "@site/../../../docs/tech-specs/adrs/adr-001-monorepo.mdx", require.resolveWeak("@site/../../../docs/tech-specs/adrs/adr-001-monorepo.mdx")],
  "9b8e5b7f": [() => import(/* webpackChunkName: "9b8e5b7f" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agents-ab4.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agents-ab4.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agents-ab4.json")],
  "9d3f2b3e": [() => import(/* webpackChunkName: "9d3f2b3e" */ "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-authors-6db.json"), "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-authors-6db.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-authors-6db.json")],
  "9d8b88bd": [() => import(/* webpackChunkName: "9d8b88bd" */ "@site/../../../docs/tech-specs/milestones/milestone-M1.mdx"), "@site/../../../docs/tech-specs/milestones/milestone-M1.mdx", require.resolveWeak("@site/../../../docs/tech-specs/milestones/milestone-M1.mdx")],
  "9e4087bc": [() => import(/* webpackChunkName: "9e4087bc" */ "@theme/BlogArchivePage"), "@theme/BlogArchivePage", require.resolveWeak("@theme/BlogArchivePage")],
  "9fc3b634": [() => import(/* webpackChunkName: "9fc3b634" */ "@site/../../../docs/tech-specs/process/templates/process-improvement.mdx"), "@site/../../../docs/tech-specs/process/templates/process-improvement.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/templates/process-improvement.mdx")],
  "a5cb6408": [() => import(/* webpackChunkName: "a5cb6408" */ "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-docusaurus-a1b.json"), "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-docusaurus-a1b.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-docusaurus-a1b.json")],
  "a6aa9e1f": [() => import(/* webpackChunkName: "a6aa9e1f" */ "@theme/BlogListPage"), "@theme/BlogListPage", require.resolveWeak("@theme/BlogListPage")],
  "a70ddb8e": [() => import(/* webpackChunkName: "a70ddb8e" */ "@site/../../../docs/tech-specs/process/agent-rules/claude.mdx"), "@site/../../../docs/tech-specs/process/agent-rules/claude.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/agent-rules/claude.mdx")],
  "a7456010": [() => import(/* webpackChunkName: "a7456010" */ "@generated/docusaurus-plugin-content-pages/default/__plugin.json"), "@generated/docusaurus-plugin-content-pages/default/__plugin.json", require.resolveWeak("@generated/docusaurus-plugin-content-pages/default/__plugin.json")],
  "a7bd4aaa": [() => import(/* webpackChunkName: "a7bd4aaa" */ "@theme/DocVersionRoot"), "@theme/DocVersionRoot", require.resolveWeak("@theme/DocVersionRoot")],
  "a94703ab": [() => import(/* webpackChunkName: "a94703ab" */ "@theme/DocRoot"), "@theme/DocRoot", require.resolveWeak("@theme/DocRoot")],
  "aba21aa0": [() => import(/* webpackChunkName: "aba21aa0" */ "@generated/docusaurus-plugin-content-docs/default/__plugin.json"), "@generated/docusaurus-plugin-content-docs/default/__plugin.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/__plugin.json")],
  "abee35a3": [() => import(/* webpackChunkName: "abee35a3" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-domain-tag-b56.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-domain-tag-b56.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-domain-tag-b56.json")],
  "acecf23e": [() => import(/* webpackChunkName: "acecf23e" */ "~blog/default/blogMetadata-default.json"), "~blog/default/blogMetadata-default.json", require.resolveWeak("~blog/default/blogMetadata-default.json")],
  "b2e305a6": [() => import(/* webpackChunkName: "b2e305a6" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-anthropic-f09.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-anthropic-f09.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-anthropic-f09.json")],
  "b34cfa08": [() => import(/* webpackChunkName: "b34cfa08" */ "@site/../../../docs/tech-specs/process/agent-rules/copilot.mdx"), "@site/../../../docs/tech-specs/process/agent-rules/copilot.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/agent-rules/copilot.mdx")],
  "b4e74118": [() => import(/* webpackChunkName: "b4e74118" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-hub-c14.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-hub-c14.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-hub-c14.json")],
  "b5781d9c": [() => import(/* webpackChunkName: "b5781d9c" */ "@site/../../../docs/tech-specs/process/core/documentation.mdx"), "@site/../../../docs/tech-specs/process/core/documentation.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/core/documentation.mdx")],
  "b7e43e8a": [() => import(/* webpackChunkName: "b7e43e8a" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-testing-fd2.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-testing-fd2.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-testing-fd2.json")],
  "b89c697e": [() => import(/* webpackChunkName: "b89c697e" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-guide-53c.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-guide-53c.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-guide-53c.json")],
  "ba0578cd": [() => import(/* webpackChunkName: "ba0578cd" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-augment-fa3.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-augment-fa3.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-augment-fa3.json")],
  "bbc4a3cc": [() => import(/* webpackChunkName: "bbc4a3cc" */ "@site/../../../docs/tech-specs/process/core/error-recovery.mdx"), "@site/../../../docs/tech-specs/process/core/error-recovery.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/core/error-recovery.mdx")],
  "c8d59050": [() => import(/* webpackChunkName: "c8d59050" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-reference-9e3.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-reference-9e3.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-reference-9e3.json")],
  "cb07a909": [() => import(/* webpackChunkName: "cb07a909" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-validation-a77.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-validation-a77.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-validation-a77.json")],
  "ccc49370": [() => import(/* webpackChunkName: "ccc49370" */ "@theme/BlogPostPage"), "@theme/BlogPostPage", require.resolveWeak("@theme/BlogPostPage")],
  "ccf59305": [() => import(/* webpackChunkName: "ccf59305" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-milestones-57c.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-milestones-57c.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-milestones-57c.json")],
  "cd52a359": [() => import(/* webpackChunkName: "cd52a359" */ "@site/../../../docs/tech-specs/process/templates/milestone-template.mdx"), "@site/../../../docs/tech-specs/process/templates/milestone-template.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/templates/milestone-template.mdx")],
  "d1822bc7": [() => import(/* webpackChunkName: "d1822bc7" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-checklist-945.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-checklist-945.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-checklist-945.json")],
  "d3808450": [() => import(/* webpackChunkName: "d3808450" */ "@site/../../../docs/tech-specs/process/core/architectural-decisions.mdx"), "@site/../../../docs/tech-specs/process/core/architectural-decisions.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/core/architectural-decisions.mdx")],
  "d4b55038": [() => import(/* webpackChunkName: "d4b55038" */ "@site/../../../docs/tech-specs/process/templates/domain-template.mdx"), "@site/../../../docs/tech-specs/process/templates/domain-template.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/templates/domain-template.mdx")],
  "d56de8d8": [() => import(/* webpackChunkName: "d56de8d8" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-copilot-3b2.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-copilot-3b2.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-copilot-3b2.json")],
  "d67ae6ec": [() => import(/* webpackChunkName: "d67ae6ec" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agent-rules-2dc.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agent-rules-2dc.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-agent-rules-2dc.json")],
  "d9f32620": [() => import(/* webpackChunkName: "d9f32620" */ "@site/blog/2021-08-26-welcome/index.md"), "@site/blog/2021-08-26-welcome/index.md", require.resolveWeak("@site/blog/2021-08-26-welcome/index.md")],
  "dc9f69d7": [() => import(/* webpackChunkName: "dc9f69d7" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-navigation-af6.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-navigation-af6.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-navigation-af6.json")],
  "ddbe459c": [() => import(/* webpackChunkName: "ddbe459c" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-docusaurus-bdf.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-docusaurus-bdf.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-docusaurus-bdf.json")],
  "df203c0f": [() => import(/* webpackChunkName: "df203c0f" */ "@theme/DocTagDocListPage"), "@theme/DocTagDocListPage", require.resolveWeak("@theme/DocTagDocListPage")],
  "dff92d18": [() => import(/* webpackChunkName: "dff92d18" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-github-46a.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-github-46a.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-github-46a.json")],
  "e02cfa96": [() => import(/* webpackChunkName: "e02cfa96" */ "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-9b5.json"), "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-9b5.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-tags-9b5.json")],
  "e0f986e7": [() => import(/* webpackChunkName: "e0f986e7" */ "@site/../../../docs/tech-specs/process/agent-rules/validation.mdx"), "@site/../../../docs/tech-specs/process/agent-rules/validation.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/agent-rules/validation.mdx")],
  "e237bb90": [() => import(/* webpackChunkName: "e237bb90" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-results-be7.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-results-be7.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-results-be7.json")],
  "e273c56f": [() => import(/* webpackChunkName: "e273c56f" */ "@site/blog/2019-05-28-first-blog-post.md"), "@site/blog/2019-05-28-first-blog-post.md", require.resolveWeak("@site/blog/2019-05-28-first-blog-post.md")],
  "e39d9123": [() => import(/* webpackChunkName: "e39d9123" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-process-improvement-5ba.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-process-improvement-5ba.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-process-improvement-5ba.json")],
  "e541576f": [() => import(/* webpackChunkName: "e541576f" */ "@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/technical-reference.md"), "@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/technical-reference.md", require.resolveWeak("@site/../../../docs/tech-specs/milestones/work-log/milestone-m0/technical-reference.md")],
  "e6c815da": [() => import(/* webpackChunkName: "e6c815da" */ "@site/../../../docs/tech-specs/milestones/milestone-M0.1.mdx"), "@site/../../../docs/tech-specs/milestones/milestone-M0.1.mdx", require.resolveWeak("@site/../../../docs/tech-specs/milestones/milestone-M0.1.mdx")],
  "e7c7788f": [() => import(/* webpackChunkName: "e7c7788f" */ "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-archive-18e.json"), "@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-archive-18e.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/kloudi-swe-agent-blog-archive-18e.json")],
  "e92aaac9": [() => import(/* webpackChunkName: "e92aaac9" */ "@site/../../../docs/tech-specs/process/core/git-workflow.mdx"), "@site/../../../docs/tech-specs/process/core/git-workflow.mdx", require.resolveWeak("@site/../../../docs/tech-specs/process/core/git-workflow.mdx")],
  "ed5f53ac": [() => import(/* webpackChunkName: "ed5f53ac" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-c29.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-c29.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-c29.json")],
  "ede4c2ad": [() => import(/* webpackChunkName: "ede4c2ad" */ "@site/../../../docs/tech-specs/milestones/milestone-M0.mdx"), "@site/../../../docs/tech-specs/milestones/milestone-M0.mdx", require.resolveWeak("@site/../../../docs/tech-specs/milestones/milestone-M0.mdx")],
  "f4f34a3a": [() => import(/* webpackChunkName: "f4f34a3a" */ "@site/blog/2021-08-01-mdx-blog-post.mdx?truncated=true"), "@site/blog/2021-08-01-mdx-blog-post.mdx?truncated=true", require.resolveWeak("@site/blog/2021-08-01-mdx-blog-post.mdx?truncated=true")],
  "fe3b3f57": [() => import(/* webpackChunkName: "fe3b3f57" */ "@site/../../../docs/tech-specs/adrs/adr-002-typescript.mdx"), "@site/../../../docs/tech-specs/adrs/adr-002-typescript.mdx", require.resolveWeak("@site/../../../docs/tech-specs/adrs/adr-002-typescript.mdx")],
  "ffd95015": [() => import(/* webpackChunkName: "ffd95015" */ "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-decisions-17c.json"), "@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-decisions-17c.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/kloudi-swe-agent-tech-specs-tags-decisions-17c.json")],};
