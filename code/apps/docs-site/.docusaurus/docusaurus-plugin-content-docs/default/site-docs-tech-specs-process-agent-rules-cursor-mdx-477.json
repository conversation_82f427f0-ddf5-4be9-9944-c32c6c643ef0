{"unversionedId": "process/agent-rules/cursor", "id": "process/agent-rules/cursor", "title": "Cursor Agent Configuration", "description": "Cursor AI specific configuration for milestone implementation", "source": "@site/../../../docs/tech-specs/process/agent-rules/cursor.mdx", "sourceDirName": "process/agent-rules", "slug": "/process/agent-rules/cursor", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/cursor", "draft": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/agent-rules/cursor.mdx", "tags": [{"label": "agent-rules", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-rules"}, {"label": "cursor", "permalink": "/kloudi-swe-agent/tech-specs/tags/cursor"}, {"label": "executable", "permalink": "/kloudi-swe-agent/tech-specs/tags/executable"}], "version": "current", "frontMatter": {"title": "Cursor Agent Configuration", "description": "Cursor AI specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "cursor", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}}