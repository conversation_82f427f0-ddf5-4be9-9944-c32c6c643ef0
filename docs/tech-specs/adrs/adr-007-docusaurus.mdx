---
title: ADR-007 — Docusaurus for Documentation Site
description: Decision to use Docusaurus 2 as the static site generator for rendering technical specifications and project documentation.
created: 2025-01-25
updated: 2025-01-25
version: 1.0.0
status: Accepted
tags: [adr, architecture, documentation, docusaurus]
authors: [nitishMehrotra]
---



<Callout emoji="📚">
<strong>Architectural Decision Record.</strong> This document captures the decision to adopt Docusaurus 2 for rendering the project's technical specifications and documentation.
</Callout>

---

## 📋 Decision Summary

**ID**: ADR-007
**Date**: 2025-01-25
**Status**: Accepted
**Deciders**: Engineering Team
**Technical Story**: Need for a static site generator to render MDX technical specifications

---

## 🎯 Context and Problem Statement

The project has extensive technical documentation written in MDX format stored in `docs/tech-specs/`, including:
- Milestone specifications
- Architectural Decision Records (ADRs)
- Domain specifications
- Process documentation
- Templates and guidelines

### Current Pain Points
- **No browsable interface**: Documentation exists only as raw MDX files
- **Poor discoverability**: No search functionality or navigation structure
- **Limited accessibility**: Requires technical knowledge to navigate file system
- **No cross-references**: Links between documents are not easily followable
- **Version tracking**: No way to track documentation versions alongside code

### Business Context
- Need to onboard new team members efficiently
- Want to share documentation with stakeholders
- Require searchable, browsable documentation for development workflow
- Need integration with existing CI/CD pipeline

---

## 🔍 Decision Drivers

### Must Have
- **MDX compatibility**: Render existing MDX files without modification
- **Search functionality**: Full-text search across all documentation
- **Navigation structure**: Organized sidebar with categories
- **CI/CD integration**: Automated builds and deployments
- **GitHub Pages compatibility**: Deploy to GitHub Pages for free hosting

### Should Have
- **Versioning support**: Track documentation versions
- **React component support**: Reuse existing Callout components
- **Mobile responsive**: Good mobile experience
- **Fast build times**: Efficient CI/CD pipeline

### Nice to Have
- **Plugin ecosystem**: Extensibility for future needs
- **Theme customization**: Brand consistency
- **Analytics integration**: Usage tracking

---

## 🎯 Decision

**We will adopt Docusaurus 2 as our documentation site generator.**

### Implementation Details
- **Location**: `code/apps/docs-site/` (treating as a deployable application)
- **Content source**: Import MDX files from `docs/tech-specs/` via relative path
- **Deployment**: GitHub Pages with automated CI/CD
- **Search**: Local search plugin for offline capability
- **Structure**: Maintain existing folder hierarchy in navigation

---

## 🤔 Alternatives Considered

### Option 1: GitBook
**Pros**: Beautiful UI, great collaboration features, built-in search
**Cons**: Requires migration from MDX, vendor lock-in, cost for private repos
**Verdict**: Rejected due to migration overhead and vendor dependency

### Option 2: VitePress
**Pros**: Fast builds, Vue ecosystem, good MDX support
**Cons**: Smaller community, less mature plugin ecosystem, team unfamiliar with Vue
**Verdict**: Rejected due to team expertise and ecosystem maturity

### Option 3: Next.js with MDX
**Pros**: Full control, React-based, team familiar with Next.js
**Cons**: Requires custom implementation, more maintenance overhead, no built-in docs features
**Verdict**: Rejected due to development overhead

### Option 4: MkDocs
**Pros**: Python ecosystem, good for technical docs, mature
**Cons**: Markdown only (no MDX), different toolchain from main project
**Verdict**: Rejected due to MDX requirement and toolchain consistency

### Option 5: Docusaurus 2 (Selected)
**Pros**:
- Native MDX support with React components
- Built-in search, navigation, and versioning
- Active development by Meta
- Strong plugin ecosystem
- GitHub Pages integration
- TypeScript support
- Familiar React ecosystem

**Cons**:
- Additional build step in CI/CD
- Learning curve for configuration
- Potential overkill for current needs

**Verdict**: **Selected** - Best balance of features, ecosystem fit, and maintenance burden

---

## 📊 Rationale

### Technical Alignment
- **React ecosystem**: Aligns with existing frontend technology (React + TypeScript)
- **MDX native**: No migration needed for existing documentation
- **Component reuse**: Can reuse existing Callout and other components
- **TypeScript support**: Consistent with project's TypeScript-first approach

### Operational Benefits
- **Zero hosting cost**: GitHub Pages deployment
- **Automated builds**: Integrates with existing CI/CD pipeline
- **Search functionality**: Local search plugin provides offline capability
- **Mobile responsive**: Good developer experience on all devices

### Future-Proofing
- **Versioning support**: Can track documentation versions alongside code releases
- **Plugin ecosystem**: Extensible for future needs (analytics, integrations, etc.)
- **Active development**: Meta-backed project with regular updates
- **Community**: Large community and extensive documentation

---

## ✅ Consequences

### Positive
- **Improved developer experience**: Browsable, searchable documentation
- **Better onboarding**: New team members can easily navigate documentation
- **Professional presentation**: Stakeholder-friendly documentation site
- **Integrated workflow**: Documentation builds alongside code
- **Search capability**: Full-text search across all specifications
- **Mobile accessibility**: Documentation accessible on mobile devices

### Negative
- **Additional complexity**: New build step and configuration to maintain
- **Build time increase**: Documentation builds add to CI/CD pipeline duration
- **Learning curve**: Team needs to learn Docusaurus configuration
- **Dependency**: Additional dependency to maintain and update

### Neutral
- **Folder structure**: Requires decision on docs-site location (resolved: `code/apps/docs-site/`)
- **Content organization**: May need to adjust navigation structure over time

---

## 🔗 References

### Related ADRs
- [ADR-001: Monorepo Structure](./adr-001-monorepo.mdx) - Establishes apps/ structure
- [ADR-002: TypeScript-First Development](./adr-002-typescript.mdx) - TypeScript alignment

### External References
- [Docusaurus 2 Documentation](https://docusaurus.io/)
- [MDX Documentation](https://mdxjs.com/)
- [GitHub Pages Documentation](https://docs.github.com/en/pages)

### Internal Documentation
- [Repository Structure](../structure.mdx)
- [Documentation Process](../process/core/documentation.mdx)

---

## 🔄 Document History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0.0 | 2025-01-25 | Initial decision record based on team discussion | nitishMehrotra |

<Callout emoji="🚀">
This ADR establishes the foundation for improved documentation accessibility and developer experience.
</Callout>
