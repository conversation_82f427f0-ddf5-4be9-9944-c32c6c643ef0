{"tag": {"label": "testing", "permalink": "/kloudi-swe-agent/tech-specs/tags/testing", "allTagsPath": "/kloudi-swe-agent/tech-specs/tags", "count": 2, "items": [{"id": "process/agent-rules/validation", "title": "Agent Configuration Validation", "description": "Tools and procedures for validating AI agent configurations", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/validation"}, {"id": "process/core/quality-assurance", "title": "Quality Assurance Process", "description": "Validation, testing, and review processes for ensuring high-quality deliverables", "permalink": "/kloudi-swe-agent/tech-specs/process/core/quality-assurance"}], "unlisted": false}}