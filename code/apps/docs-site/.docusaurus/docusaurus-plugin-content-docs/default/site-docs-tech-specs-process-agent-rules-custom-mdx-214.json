{"id": "process/agent-rules/custom", "title": "Custom Agent Configuration Template", "description": "Template for configuring custom AI agents for milestone implementation", "source": "@site/../../../docs/tech-specs/process/agent-rules/custom.mdx", "sourceDirName": "process/agent-rules", "slug": "/process/agent-rules/custom", "permalink": "/kloudi-swe-agent/tech-specs/process/agent-rules/custom", "draft": false, "unlisted": false, "editUrl": "https://github.com/nitishmehrotra/kloudi-swe-agent/tree/main/docs/tech-specs/../../../docs/tech-specs/process/agent-rules/custom.mdx", "tags": [{"inline": true, "label": "agent-rules", "permalink": "/kloudi-swe-agent/tech-specs/tags/agent-rules"}, {"inline": true, "label": "custom", "permalink": "/kloudi-swe-agent/tech-specs/tags/custom"}, {"inline": true, "label": "template", "permalink": "/kloudi-swe-agent/tech-specs/tags/template"}, {"inline": true, "label": "executable", "permalink": "/kloudi-swe-agent/tech-specs/tags/executable"}], "version": "current", "frontMatter": {"title": "Custom Agent Configuration Template", "description": "Template for configuring custom AI agents for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "custom", "template", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}}